import { useEffect, useState } from "react";
import "./question-page.css";
import { useParams } from "react-router-dom";
import Editor from "@monaco-editor/react";
import { questionData } from "../../utils/problems";
import axios from "axios";

const Codexuslab = () => {
  const [code, setCode] = useState("# Write your Python code here...");
  const [time, setTime] = useState(0);
  const [output, setOutput] = useState("");
  const [isCodeRun, setIsCodeRun] = useState(false);
  const [startTimer, setStartTimer] = useState(false);
  const { id } = useParams();
  const [question,setQuestion] = useState({});

  useEffect( ()=>{
    async  function fetchData() {
      console.log(question)
      const  response = await axios.get(`http://localhost:8000/api/questions/${id}`);
      console.log(question)
      console.log(response.data)
      setQuestion(response.data); 
    }
  
fetchData();

  },[]);

  const runCode = () => {
    console.log("Code is running fine okay...");
    const mockOutput = `Output: [2,4,3,1]`;
    setOutput(mockOutput);
    setIsCodeRun(true);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins < 10 ? "0" : ""}${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  useEffect(() => {
    let timerInterval;
    if (startTimer) {
      timerInterval = setInterval(() => {
        setTime((prevTime) => prevTime + 1);
      }, 1000);
    }
    return () => clearInterval(timerInterval);
  }, [startTimer]);



  const handleStartTimer = () => {
    setStartTimer(true);
  };

  const handleStopTimer = () => {
    setStartTimer(false);
  };

  if (!question) {
    return <div>Question not found!</div>;
  }
  
  return (
    <div>
      <div className="container">
        <div className="row">
          {/* Description Section */}
          <div className="col-md-6 description-section">
            <h2 className="animate_animated animate_fadeInLeft font-bold text-xl mb-2">
              <i className="fas fa-file-alt"></i> {question?.title}
            </h2>
            <p className="my-2">{question?.description}</p>
            <h2 className="animate_animated animate_fadeInLeft ">
              Difficulty: <span className="font-semibold text-xl ml-2 my-2">{question?.difficulty}</span> 
            </h2>
            <h2 className="animate_animated animate_fadeInLeft my-2">
              Topics: <span className="font-semibold text-md ml-2 ">{question?.tags?.join("    ")}</span> 
            </h2>
            <h5 className="animate_animated animate_fadeInLeft">
              <i className="fas fa-lightbulb"></i> Example 1:
            </h5>
            <pre>
              <code>Input: nums = [3,1,2,4]</code>
              <code>Output: [2,4,3,1]</code>
            </pre>
            <h5 className="animate_animated animate_fadeInLeft">
              <i className="fas fa-lightbulb"></i> Example 2:
            </h5>
            <pre>
              <code>Input: nums = [0]</code>
              <code>Output: [0]</code>
            </pre>
            <h5 className="animate_animated animate_fadeInLeft">
              <i className="fas fa-check-circle"></i> Constraints:
            </h5>
            <ul>
              <li>1 &lt;= nums.length &lt;= 5000</li>
              <li>0 &lt;= nums[i] &lt;= 5000</li>
            </ul>
          </div>

          {/* Code Editor Section */}
          <div className="editor-section">
            <div className="editor-header">
              <select className="language-selector">
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="java">Java</option>
              </select>

              {startTimer ? (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    color: "red",
                  }}
                  className="timer"
                  onClick={handleStopTimer}
                >
                  <i
                    className="fas fa-clock"
                    style={{ marginRight: "8px" }}
                  ></i>
                  Stop {formatTime(time)}
                </div>
              ) : (
                <div
                  style={{ display: "flex", alignItems: "center" }}
                  className="timer"
                  onClick={handleStartTimer}
                >
                  <i
                    className="fas fa-clock"
                    style={{ marginRight: "8px" }}
                  ></i>
                  Start {formatTime(time)}
                </div>
              )}

              <button className="btn-primary" onClick={runCode}>
                Run Code
              </button>
            </div>
            <Editor
              height="400px"
              defaultLanguage="python"
              theme="vs-dark"
              value={code}
              onChange={setCode}
            />
            {isCodeRun && (
              <div className="output-section">
                <h3>Output</h3>
                <div className="output-testcase">
                  <h4>Test Case 1:</h4>
                  <div className="output-row">
                    <div className="expected-output">
                      <h5>Expected Output:</h5>
                      <pre>[5, 4, 3, 2, 1]</pre>
                    </div>
                    <div className="your-output">
                      <h5>Your Output:</h5>
                      <pre>{output}</pre>
                    </div>
                  </div>
                </div>
                <div className="output-testcase">
                  <h4>Test Case 2:</h4>
                  <div className="output-row">
                    <div className="expected-output">
                      <h5>Expected Output:</h5>
                      <pre>[9, 8, 7, 6, 5]</pre>
                    </div>
                    <div className="your-output">
                      <h5>Your Output:</h5>
                      <pre>{output}</pre>
                    </div>
                  </div>
                </div>
                <div className="output-testcase">
                  <h4>Test Case 3:</h4>
                  <div className="output-row">
                    <div className="expected-output">
                      <h5>Expected Output:</h5>
                      <pre>[0]</pre>
                    </div>
                    <div className="your-output">
                      <h5>Your Output:</h5>
                      <pre>{output}</pre>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Codexuslab;