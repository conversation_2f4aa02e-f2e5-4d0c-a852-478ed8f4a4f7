import { useState } from "react";

const InterviewChecklistNew = ({ interviewChecklist, showPremiumOverlay }) => {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleAnswer = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  // CSS Animation Styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes slideInLeft {
      from { opacity: 0; transform: translateX(-30px); }
      to { opacity: 1; transform: translateX(0); }
    }
    .checklist-container {
      opacity: 0;
      animation: fadeIn 0.6s ease forwards;
    }
    .checklist-header {
      opacity: 0;
      animation: slideInLeft 0.5s ease forwards 0.6s;
    }
    .checklist-icon {
      transition: transform 0.3s ease;
    }
    .checklist-icon:hover {
      transform: rotate(10deg) scale(1.1);
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6 shadow-lg checklist-container">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      <div
        className="flex items-center gap-4 mb-6 pb-4 border-b border-white/10 checklist-header"
        id="interview-checklist"
      >
        <div
          className="text-white p-3 bg-[#303246]/60 backdrop-blur-lg rounded-xl shadow-md checklist-icon border border-white/10"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white">
          Interview Checklist
        </h2>
      </div>

      <ul className="space-y-3">
        {interviewChecklist.map((item, index) => (
          <li
            key={index}
            className="bg-[#1e293b]/30 backdrop-blur-lg rounded-xl shadow-sm border border-white/10 overflow-hidden checklist-item"
            style={{ 
              opacity: 0, 
              animation: `fadeIn 0.4s ease forwards ${0.8 + index * 0.05}s` 
            }}
          >
            <div
              className="flex items-center justify-between p-4 cursor-pointer hover:bg-white/5 transition-colors duration-200"
              onClick={() => toggleAnswer(index)}
            >
              <span className="font-semibold text-gray-200 flex-1">
                {index + 1}. {item.question}
              </span>
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className={`h-5 w-5 text-blue-400 ml-4 transform transition-transform duration-300 ${activeIndex === index ? 'rotate-180' : ''}`}
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
            
            <div
              className={`px-4 pb-4 transition-all duration-300 overflow-hidden ${activeIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}
            >
              <div className="bg-[#1e293b]/50 backdrop-blur-lg p-4 rounded-lg border-l-4 border-blue-500/50">
                <p className="text-gray-300 leading-relaxed">
                  {item.answer}
                </p>
              </div>
            </div>
          </li>
        ))}
      </ul>

      {/* Premium CTA */}
      <div className="mt-8 p-6 bg-[#303246]/30 backdrop-blur-lg rounded-xl border border-white/10 text-center">
        <h3 className="text-xl font-bold text-white mb-2">Want a complete interview preparation guide?</h3>
        <p className="text-gray-300 mb-4">Upgrade to premium for a comprehensive system design interview preparation guide.</p>
        <button 
          onClick={showPremiumOverlay}
          className="bg-[#303246]/60 backdrop-blur-lg text-white px-6 py-3 rounded-xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-white/10 inline-flex items-center"
        >
          <span className="relative z-10">Get Premium Access</span>
          <span className="ml-2 relative z-10">🚀</span>
        </button>
      </div>
    </div>
  );
};

export default InterviewChecklistNew;