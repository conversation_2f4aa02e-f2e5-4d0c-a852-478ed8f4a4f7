import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import axios from "axios";
import { chapterData } from "./data/chapterData.js";

const Chapter = () => {
  const [questions, setQuestions] = useState([]);
  const [loading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeChapter, setActiveChapter] = useState(null);

  const toggleChapter = (index) => {
    setActiveChapter(activeChapter === index ? null : index);
  };

  useEffect(() => {
    // Always set mock data first to avoid undefined/null errors
    setQuestions(chapterData || []);
    setIsLoading(false);
    
    // Then try to fetch from API (the mock data will already be rendered)
    axios
      .get("/api/questions")
      .then((res) => {
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          setQuestions(res.data);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        console.error("API Error:", err);
        // Already using mock data, just log the error
        setIsLoading(false);
      });
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  if (loading) {
    return (
      <div className="py-8 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full"
        />
        <span className="ml-4 text-lg text-blue-100/80">Loading chapters...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8 flex items-center justify-center">
        <div className="bg-red-500/20 backdrop-blur-lg border border-red-400/30 text-red-200 px-6 py-4 rounded-lg">
          <p className="font-medium">❌ {error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-5xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            📚 Course Chapters
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
            Python Programming{" "}
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Chapters
            </span>
          </h2>
          
          <p className="text-xl text-blue-100/80 max-w-3xl mx-auto leading-relaxed">
            Explore comprehensive chapters covering all aspects of Python programming
          </p>
        </motion.div>

        {/* Chapters List */}
        <motion.div variants={itemVariants} className="space-y-6">
          {Array.isArray(questions) && questions.map((question, index) => (
            <motion.div
              key={question?._id || `chapter-${index}`}
              variants={itemVariants}
              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300"
            >
              <motion.div
                onClick={() => toggleChapter(index)}
                className="p-6 cursor-pointer hover:bg-white/5 transition-colors duration-200 flex items-center justify-between"
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center text-white font-bold text-lg">
                    {index + 1}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-1" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                      {question.title}
                    </h3>
                    <p className="text-blue-100/60 text-sm">
                      Click to view chapter details
                    </p>
                  </div>
                </div>
                
                <motion.div
                  animate={{ rotate: activeChapter === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-2xl text-blue-100/60"
                >
                  ↓
                </motion.div>
              </motion.div>

              <AnimatePresence>
                {activeChapter === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.4 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm border-t border-white/10">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-3" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                            Chapter Description
                          </h4>
                          <p className="text-blue-100/80 leading-relaxed">
                            {question.description}
                          </p>
                        </div>
                        
                        <div className="space-y-4">
                          <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-4 rounded-xl">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-green-400">✓</span>
                              <span className="font-medium text-white" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)' }}>Learning Objectives</span>
                            </div>
                            <p className="text-blue-100/80 text-sm">
                              Master key concepts and practical applications
                            </p>
                          </div>
                          
                          <div className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-4 rounded-xl">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-blue-400">📊</span>
                              <span className="font-medium text-white" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)' }}>Difficulty Level</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="flex space-x-1">
                                {[...Array(5)].map((_, i) => (
                                  <div
                                    key={i}
                                    className={`w-2 h-2 rounded-full ${
                                      i < (typeof question.difficulty === 'number' ? question.difficulty : 3)
                                        ? "bg-gradient-to-r from-purple-400 to-blue-400"
                                        : "bg-white/20"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-blue-100/80 text-sm">
                                {typeof question.difficulty === 'number' ? question.difficulty : 
                                 (question.difficulty === 'Easy' ? 1 : 
                                  question.difficulty === 'Medium' ? 3 : 
                                  question.difficulty === 'Hard' ? 5 : 3)}/5
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          variants={itemVariants}
          className="mt-16 grid md:grid-cols-3 gap-6"
        >
          {[
            {
              icon: "📖",
              number: questions.length,
              label: "Total Chapters",
              color: "from-purple-500 to-blue-500",
            },
            {
              icon: "🎯",
              number: "100%",
              label: "Practical Focus",
              color: "from-blue-500 to-cyan-500",
            },
            {
              icon: "⭐",
              number: "Expert",
              label: "Level Content",
              color: "from-green-500 to-teal-500",
            },
          ].map((stat, index) => (
            <motion.div
              key={index}
              whileHover={{ y: -5 }}
              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-6 rounded-xl shadow-lg text-center hover:shadow-xl transition-all duration-300"
            >
              <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center text-white text-2xl`}>
                {stat.icon}
              </div>
              <div className="text-3xl font-bold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                {stat.number}
              </div>
              <div className="text-blue-100/80">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Chapter;
