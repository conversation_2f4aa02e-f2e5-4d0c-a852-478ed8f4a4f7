import { lazy, Suspense, useEffect, useState } from "react";
import { LoadingSpinner } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";

const Section = ({ labPage }) => {
  const id = labPage?._id;
  const [sectionCard, setSectionCard] = useState([]);
  const [activeContent, setActiveContent] = useState("Introduction");
  const [sectionCardId,setSectionCardId] = useState("");

  console.log(sectionCardId)

  useEffect(() => {
    const fetchCardSection = async () => {
      try {
        const { data } = await axiosInstance.get(
          `/labsection/all/${id}`,
        );

        if (data.success) {
          setSectionCard(data.sections);
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };

    if (id) fetchCardSection();
  }, [id]);

  const onCardSelect = (id, name) => {
    setActiveContent(name);
    setSectionCardId(id)
  };

  const sanitize = (str) => str.replace(/\s+/g, "");

  const componentsMap = {
    Introduction: lazy(() => import("../labDetails/Introduction")),
    FAQ: lazy(() => import("../labDetails/FAQ")),
    LabEnvironment: lazy(() => import("../labDetails/LabEnvironment")),
  };

  const RenderedComponent = componentsMap[sanitize(activeContent)];
  return (
    <div className="container mx-auto px-4 transition-all duration-300 !pt-0 !mt-0">
      {/* Section Header */}
      <div className="text-center !mt-0 !mb-0 !pt-0 !pb-0">
        <h2 className={`text-5xl font-bold mb-4 text-white`}>
          {labPage?.title}
        </h2>
        <p className={`max-w-2xl mx-auto text-gray-400`}>
          {labPage?.description}
        </p>
      </div>

      {/* Course Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8 mb-8">
        {sectionCard?.map((section) => (
          <div
            key={section._id}
            onClick={() => onCardSelect(section._id, section.title)}
            className="relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-2xl group bg-gradient-to-br from-[#118c6e] to-[#0d6b56] border border-white/10"
          >
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="relative z-10 p-6 text-white">
              <div className="mb-4 opacity-80 group-hover:opacity-100 transition-opacity text-white">
                {section.icon}
              </div>
              <h3 className="text-xl font-bold mb-2 group-hover:text-white transition-colors">
                {section.title}
              </h3>
              <p className="text-sm opacity-80 group-hover:opacity-100 transition-opacity">
                {section.description}
              </p>
            </div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>
        ))}
      </div>

      {/* Selected Content */}
      {
        <div className="mt-12 content-animate">
          <style
            dangerouslySetInnerHTML={{
              __html: `
              @keyframes fadeInUp {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .content-animate {
                animation: fadeInUp 0.5s ease-out forwards;
              }
            `,
            }}
          />
          <Suspense
            fallback={
              <div className="text-white">
                <LoadingSpinner />
              </div>
            }
          >
            {RenderedComponent ? (
              <RenderedComponent labPage={labPage} sectionCardId={sectionCardId}/>
            ) : (
              <div className="text-red-600 font-bold text-center">
                Page not found.
              </div>
            )}
          </Suspense>
        </div>
      }
    </div>
  );
};

export default Section;
