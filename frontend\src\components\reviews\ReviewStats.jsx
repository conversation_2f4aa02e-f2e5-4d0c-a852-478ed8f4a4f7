import React from 'react';
import { motion } from 'framer-motion';
import { FaStar, FaUsers, FaChartBar } from 'react-icons/fa';

const ReviewStats = ({ stats, showDetailed = true }) => {
  const { totalReviews, averageRating, ratingDistribution } = stats;

  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, index) => (
      <FaStar
        key={index}
        className={`${
          index < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        } text-lg`}
      />
    ));
  };

  const getPercentage = (count) => {
    if (totalReviews === 0) return 0;
    return Math.round((count / totalReviews) * 100);
  };

  if (totalReviews === 0) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-600 p-6 text-center">
        <div className="text-gray-400 mb-4">
          <FaChartBar className="text-4xl mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-300 mb-2">
          No Reviews Yet
        </h3>
        <p className="text-gray-400">
          This course hasn't received any reviews yet.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-600 p-6"
    >
      {/* Overall Rating */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-2">
          {renderStars(averageRating)}
        </div>
        <div className="text-3xl font-bold text-white mb-1">
          {averageRating}
        </div>
        <div className="flex items-center justify-center text-gray-300">
          <FaUsers className="mr-2" />
          <span>{totalReviews} review{totalReviews !== 1 ? 's' : ''}</span>
        </div>
      </div>

      {/* Detailed Rating Distribution */}
      {showDetailed && (
        <div className="space-y-3">
          <h4 className="font-semibold text-white mb-3">Rating Distribution</h4>
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = ratingDistribution[rating] || 0;
            const percentage = getPercentage(count);
            
            return (
              <div key={rating} className="flex items-center space-x-3">
                <div className="flex items-center space-x-1 w-12">
                  <span className="text-sm font-medium text-gray-300">
                    {rating}
                  </span>
                  <FaStar className="text-yellow-400 text-xs" />
                </div>

                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${percentage}%` }}
                    transition={{ duration: 0.8, delay: (5 - rating) * 0.1 }}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full"
                  />
                </div>
                
                <div className="w-12 text-right">
                  <span className="text-sm text-gray-300">
                    {count}
                  </span>
                </div>

                <div className="w-12 text-right">
                  <span className="text-sm text-gray-400">
                    {percentage}%
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Quick Stats */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-400">
              {getPercentage((ratingDistribution[5] || 0) + (ratingDistribution[4] || 0))}%
            </div>
            <div className="text-sm text-gray-300">Positive Reviews</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {averageRating >= 4.5 ? 'Excellent' :
               averageRating >= 4.0 ? 'Very Good' :
               averageRating >= 3.5 ? 'Good' :
               averageRating >= 3.0 ? 'Average' : 'Below Average'}
            </div>
            <div className="text-sm text-gray-300">Overall Rating</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ReviewStats;
