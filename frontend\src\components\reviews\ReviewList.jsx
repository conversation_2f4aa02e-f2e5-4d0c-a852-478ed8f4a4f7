import React from 'react';
import { motion } from 'framer-motion';
import { FaStar, FaUser, FaCalendarAlt, FaReply } from 'react-icons/fa';

const ReviewList = ({ reviews, showAdminReply = true }) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, index) => (
      <FaStar
        key={index}
        className={`${
          index < rating ? 'text-yellow-400' : 'text-gray-300'
        } text-sm`}
      />
    ));
  };

  if (!reviews || reviews.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <FaStar className="text-4xl mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-300 mb-2">
          No Reviews Yet
        </h3>
        <p className="text-gray-400">
          Be the first to share your experience with this course!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {reviews.map((review, index) => (
        <motion.div
          key={review.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-600 p-6"
        >
          {/* Review Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                {review.userName?.charAt(0)?.toUpperCase() || <FaUser />}
              </div>
              <div>
                <h4 className="font-semibold text-white">
                  {review.userName || 'Anonymous User'}
                </h4>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <FaCalendarAlt className="text-xs" />
                  <span>{formatDate(review.date)}</span>
                </div>
              </div>
            </div>
            
            {/* Rating */}
            <div className="flex items-center space-x-1">
              {renderStars(review.rating)}
              <span className="ml-1 text-sm font-medium text-gray-300">
                {review.rating}/5
              </span>
            </div>
          </div>

          {/* Review Content */}
          <div className="mb-4">
            <h5 className="font-semibold text-white mb-2">
              {review.title}
            </h5>
            <p className="text-gray-300 leading-relaxed">
              {review.comment}
            </p>
          </div>

          {/* Admin Reply */}
          {showAdminReply && review.adminReply && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mt-4 p-4 bg-blue-900/30 rounded-lg border-l-4 border-blue-400"
            >
              <div className="flex items-center space-x-2 mb-2">
                <FaReply className="text-blue-400 text-sm" />
                <span className="font-semibold text-blue-300 text-sm">
                  Admin Response
                </span>
              </div>
              <p className="text-blue-200 text-sm">
                {review.adminReply}
              </p>
            </motion.div>
          )}

          {/* Course Name (for admin view) */}
          {review.courseName && (
            <div className="mt-3 pt-3 border-t border-gray-600">
              <span className="inline-block px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-full">
                {review.courseName}
              </span>
            </div>
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default ReviewList;
