import React from "react";
import { motion } from "framer-motion";
import ReusableCodeEditor from "../../../components/ui/ReusableCodeEditor";

const SQLLabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const practiceProblems = [
    {
      title: "Employee Database Queries",
      difficulty: "Easy",
      description: "Practice basic SELECT queries on an employee database",
      tags: ["SELECT", "WHERE", "ORDER BY"]
    },
    {
      title: "Multi-Table Joins",
      difficulty: "Medium",
      description: "Join multiple tables to retrieve complex data relationships",
      tags: ["INNER JOIN", "LEFT JOIN", "Multiple Tables"]
    },
    {
      title: "Advanced Aggregations",
      difficulty: "Medium",
      description: "Use GROUP BY with complex aggregation functions",
      tags: ["GROUP BY", "HAVING", "Aggregation"]
    },
    {
      title: "Window Functions",
      difficulty: "Hard",
      description: "Implement advanced analytics with window functions",
      tags: ["OVER", "PARTITION BY", "ROW_NUMBER"]
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-teal-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">SQL Practice Lab</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Interactive SQL Practice Environment</h3>
          <p className="text-gray-300">
            Sharpen your SQL skills with our interactive database environment. Solve problems, 
            test your queries, and track your progress.
          </p>
        </div>
        
        {/* Practice Problems */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {practiceProblems.map((problem, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 bg-gray-800/30 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={showPremiumOverlay}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-white">{problem.title}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  problem.difficulty === "Easy" ? "bg-green-900/50 text-green-300 border border-green-700/30" :
                  problem.difficulty === "Medium" ? "bg-yellow-900/50 text-yellow-300 border border-yellow-700/30" :
                  "bg-red-900/50 text-red-300 border border-red-700/30"
                }`}>
                  {problem.difficulty}
                </span>
              </div>
              <p className="text-sm text-gray-300 mb-3">{problem.description}</p>
              <div className="flex flex-wrap gap-2">
                {problem.tags.map((tag, idx) => (
                  <span key={idx} className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-md border border-blue-700/30">
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* SQL Code Editor */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <ReusableCodeEditor 
            showPremiumOverlay={showPremiumOverlay} 
            challenges={[
              {
                id: "sql-challenge-1",
                title: "Basic SQL Queries",
                difficulty: "Beginner",
                description: "Practice writing basic SQL queries for data retrieval.",
                requirements: [
                  "Write a SELECT query to retrieve all columns from a table",
                  "Filter data using WHERE clause",
                  "Sort results using ORDER BY"
                ],
                template: `-- SQL Challenge: Basic Queries

-- 1. Select all columns from the 'employees' table


-- 2. Select employees with salary greater than 50000


-- 3. Select employees ordered by last name in ascending order

`
              },
              {
                id: "sql-challenge-2",
                title: "SQL Joins and Aggregations",
                difficulty: "Intermediate",
                description: "Practice writing SQL queries with joins and aggregate functions.",
                requirements: [
                  "Write queries using INNER JOIN and LEFT JOIN",
                  "Use aggregate functions like COUNT, SUM, AVG",
                  "Group results using GROUP BY"
                ],
                template: `-- SQL Challenge: Joins and Aggregations

-- 1. Join 'employees' and 'departments' tables to get employee names with their department names


-- 2. Calculate the average salary for each department


-- 3. Find the department with the highest number of employees

`
              }
            ]} 
          />
        </motion.div>
      </div>
    </div>
  );
};

export default SQLLabEnvironment;