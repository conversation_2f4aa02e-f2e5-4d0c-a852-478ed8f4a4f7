import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const JavaFullStackFAQS = ({ onBackToCourse }) => {
  const [openFaq, setOpenFaq] = useState(null);
  
  const faqs = [
    {
      question: "What is Java Full Stack Development?",
      answer: "Java Full Stack Development involves using Java for backend development and integrating it with frontend technologies. Typically, it includes using frameworks like Spring Boot for the backend, and combining it with frontend technologies like React, Angular, or Thymeleaf to create complete web applications."
    },
    {
      question: "Which Java frameworks are best for full stack development?",
      answer: "The most popular Java framework for full stack development is Spring Boot, which provides a comprehensive ecosystem for building web applications. Other options include Java EE (now Jakarta EE), Micronaut, and Quarkus. Spring Boot is widely used due to its extensive features, large community, and excellent integration capabilities."
    },
    {
      question: "How does Java integrate with frontend technologies?",
      answer: "Java can integrate with frontend technologies in several ways: 1) Using template engines like Thymeleaf or JSP for server-side rendering, 2) Building RESTful APIs with Spring Boot that frontend JavaScript frameworks can consume, 3) Using Spring MVC to serve static files generated by frontend build tools, or 4) Implementing WebSockets for real-time communication between Java backend and JavaScript frontend."
    },
    {
      question: "What databases work well with Java full stack applications?",
      answer: "Java works well with virtually all databases through JDBC and JPA. For relational databases, PostgreSQL, MySQL, and Oracle are commonly used with Hibernate or Spring Data JPA. For NoSQL databases, MongoDB (with Spring Data MongoDB), Cassandra, and Redis are popular choices. The choice depends on your application's specific requirements."
    },
    {
      question: "How do I deploy a Java full stack application?",
      answer: "Common deployment options for Java full stack applications include: 1) Traditional application servers like Tomcat or Jetty, 2) Containerization with Docker and deployment to Kubernetes clusters, 3) Cloud platforms like AWS, Google Cloud, or Azure using their managed services, or 4) Platform-as-a-Service providers like Heroku or AWS Elastic Beanstalk."
    },
    {
      question: "What are the advantages of using Java for full stack development?",
      answer: "Advantages of Java for full stack development include: 1) Enterprise-grade reliability and performance, 2) Strong typing and robust error handling, 3) Excellent framework options like Spring Boot, 4) Strong community support and extensive documentation, 5) Great for large-scale applications, and 6) Excellent integration with various databases and third-party services."
    }
  ];

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Frequently Asked Questions</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Common Java Full Stack Questions</h3>
          <p className="text-gray-300">
            Find answers to frequently asked questions about Java full stack development, frameworks, 
            deployment strategies, and best practices.
          </p>
        </div>
        
        {/* FAQs */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleFaq(index)}
                className="w-full flex items-center justify-between p-4 text-left bg-gray-800/30 hover:bg-gray-700/40 transition-colors"
              >
                <h4 className="font-medium text-white">{faq.question}</h4>
                <motion.div
                  animate={{ rotate: openFaq === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <FaChevronDown className="text-gray-300" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="p-4 bg-gray-800/20">
                      <p className="text-gray-300">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default JavaFullStackFAQS;