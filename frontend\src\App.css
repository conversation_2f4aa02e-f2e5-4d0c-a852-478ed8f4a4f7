/* Import mobile responsiveness fixes */
@import './styles/mobile-responsive.css';

.contentone-wrapperrr {
  background-color: #ffffff;
  border-radius: 12px;
  width: 100%;
  padding-bottom: 20px;
  position: relative;
  overflow-x: auto;
}

/* Hide scrollbar for Webkit browsers (Chrome, Safari) */
.contentone-wrapperrr::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.contentone-wrapperrr {
  scrollbar-width: none;
}

/* Hide scrollbar for Internet Explorer and Edge */
.contentone-wrapperrr {
  -ms-overflow-style: none;
}

.content-header {
  background-color: var(--header-background);
  color: #ffffff;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.grid-layout {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 20px;
  padding: 15px;
  width: 100%;
  box-sizing: border-box;
}

.item-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 0 0 auto;
  max-width: 120px;
  min-width: 120px;
}

.item-card:hover {
  background-color: #e9ecef;
}

.card-icon {
  font-size: 22px;
  color: var(--icon-color);
  margin-bottom: 5px;
}

.card-title {
  font-weight: bold;
  font-size: 14px;
  color: #002366;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .grid-layout {
    gap: 15px;
  }
}

@media (max-width: 992px) {
  .grid-layout {
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .grid-layout {
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .grid-layout {
    gap: 5px;
    padding: 10px;
  }

  .item-card {
    max-width: 100px;
    min-width: 100px;
  }
}

/* 10 buttons end */

/* Style for the loading screen */
#loading-screen {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Simple loading icon style */
.loading-icon {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Style for the main content once loaded */
#main-content {
  display: none;
}

:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #e74c3c;
  --text-color: #ecf0f1;
  --background-color: #f9f9f9;
  /* KodeKloud Theme - Global Variables (Exact Match) */
  --kodekloud-primary: #2563eb;         /* Blue-600 */
  --kodekloud-primary-dark: #1d4ed8;    /* Blue-700 */
  --kodekloud-secondary: #4f46e5;       /* Indigo-600 */
  --kodekloud-accent: #7c3aed;          /* Violet-600 */
  --kodekloud-text: #0f172a;            /* Slate-900 - Darker text */
  --kodekloud-text-light: #64748b;      /* Slate-500 */
  --kodekloud-bg-light: #f8fafc;        /* Slate-50 */
  --kodekloud-gradient-start: #0f172a;  /* Slate-900 */
  --kodekloud-gradient-middle: #1e3b8a; /* Custom blue-900 */
  --kodekloud-gradient-end: #4c1d95;    /* Purple-900 */
  --kodekloud-card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

/* KodeKloud Theme Variables */
:root {
  --kodekloud-primary: #1a3c50;
  --kodekloud-secondary: #010509;
  --kodekloud-accent: #1a3c50;
  --kodekloud-blue: #3b82f6;
  --kodekloud-indigo: #6366f1;
  --kodekloud-purple: #8b5cf6;
  --kodekloud-text-light: #f8fafc;
  --kodekloud-text-muted: #94a3b8;
}

/* Global background for KodeKloud theme */
body {
  background: linear-gradient(114deg,#1a3c50,#010509);
  color: var(--kodekloud-text-light);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--primary-color);
  line-height: 1.6;
}

/* Ensure content doesn't get hidden under fixed navbar */
body {
  padding-top: 80px;
  scroll-padding-top: 80px;
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
  body {
    padding-top: 80px; /* Consistent navbar height on mobile */
  }
}

/* Styles for proper positioning with sidebar and navbar */
.sidebar-open {
  margin-left: 280px;
  transition: margin 0.3s ease;
}

/* Main content padding */
.main-content {
  padding-top: 1rem;
}

.header {
  background: rgb(28, 54, 28);
  color: var(--text-color);
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.code-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  font-family: monospace;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
}

.header-content {
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.cta-button {
  display: inline-block;
  background-color: var(--accent-color);
  color: var(--text-color);
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.nav-box {
  flex: 1;
  background-color: #fff;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 0 0.5rem;
}

.nav-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.nav-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.main-content {
  background-color: #fff;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.main-content.active {
  display: block;
}

.content-item {
  background-color: #f1f1f1;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.content-title {
  background-color: var(--secondary-color);
  color: var(--text-color);
  padding: 1rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-body {
  padding: 1rem;
}

.chapter-list {
  list-style-type: none;
  padding: 0;
}

.chapter-item {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chapter-header {
  background-color: var(--secondary-color);
  color: var(--text-color);
  padding: 1rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chapter-content {
  padding: 1rem;
}

.chapter-content ul {
  list-style-type: none;
  padding-left: 1rem;
}

.chapter-content li {
  margin-bottom: 0.5rem;
}

.faq-item {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.faq-question {
  background-color: var(--secondary-color);
  color: var(--text-color);
  padding: 1rem;
  cursor: pointer;
}

.faq-answer {
  padding: 1rem;
  display: none;
}

.premium-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.premium-content {
  background-color: #fff;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.premium-title {
  font-size: 1.75rem;
  margin-bottom: 1rem;
}

.premium-price {
  font-size: 1.25rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.premium-features {
  list-style-type: none;
  margin: 0;
  padding: 0;
  margin-bottom: 1rem;
}

.premium-features li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
}

.premium-features i {
  margin-right: 10px;
  color: var(--accent-color);
}

.premium-overlay .cta-button {
  background-color: var(--primary-color);
  color: var(--text-color);
  padding: 0.7rem 1.2rem;
  border-radius: 25px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0.5rem;
  font-size: 1rem;
}

.premium-overlay .cta-button:hover {
  background-color: #1a252f;
}

.close-button {
  background: var(--accent-color);
  color: var(--text-color);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.close-button:hover {
  background-color: #c0392b;
}

@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
  }

  .nav-box {
    margin: 0.5rem 0;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-subtitle {
    font-size: 1rem;
  }
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  font-weight: bold;
}

.chapter-content {
  padding: 10px;
  border: 1px solid #ddd;
  margin-top: 10px;
  background-color: #f9f9f9;
}

/* Dark theme overrides for chapter content */
.bg-gradient-to-br .chapter-content {
  background-color: transparent !important;
  border-color: rgba(59, 130, 246, 0.3);
}

.solution {
  background-color: #eee;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
}

/* Dark theme overrides for solution blocks */
.bg-gradient-to-br .solution {
  background-color: transparent !important;
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #e5e7eb;
}

.course-show-solution {
  padding: 6px 16px;
  background-color: #3498db;
  border: none;
  outline: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  color: #ddd;
}

/* loader */

.parents-loaders {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

.parents-loaders {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed; 
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8); 
  z-index: 9999; 
  font-family: Arial, sans-serif;
}

.loaders-containers {
  display: flex;
  gap: 1rem;
}

.loaders-bars {
  width: 10px;
  height: 80px;
  background: linear-gradient(45deg, #00c6ff, #0072ff);
  animation: bounce 1.2s infinite ease-in-out;
}

.loaders-bars:nth-child(2) {
  animation-delay: 0.2s;
}

.loaders-bars:nth-child(3) {
  animation-delay: 0.4s;
}

.loaders-bars:nth-child(4) {
  animation-delay: 0.6s;
}

.loaders-bars:nth-child(5) {
  animation-delay: 0.8s;
}

@keyframes bounce {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.8);
  }
}

/* KodeKloud Style Course Cards - Exact Match */
.kodekloud-course-card {
  border-radius: 0.75rem;
  background-color: white;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: var(--kodekloud-card-shadow);
  border: 1px solid #f1f5f9;
}

.kodekloud-course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.kodekloud-course-card .course-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.kodekloud-course-card .course-content {
  padding: 1.25rem;
}

.kodekloud-course-card .course-title {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  color: var(--kodekloud-text);
  line-height: 1.4;
}

.kodekloud-course-card .course-instructor {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--kodekloud-primary);
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
}

.kodekloud-course-card .course-instructor::before {
  content: "";
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: #e2e8f0;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.kodekloud-course-card .course-button {
  display: inline-block;
  width: 100%;
  text-align: center;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  background: linear-gradient(to right, var(--kodekloud-primary), var(--kodekloud-primary-dark));
  color: white;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: all 0.2s;
  box-shadow: 0 4px 10px rgba(37, 99, 235, 0.2);
}

.kodekloud-course-card .course-button:hover {
  background-color: var(--kodekloud-primary-dark);
}

/* KodeKloud Section Headers */
.kodekloud-section-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  color: var(--kodekloud-text);
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .kodekloud-section-title {
    font-size: 2.5rem;
  }
}

.kodekloud-section-description {
  text-align: center;
  max-width: 48rem;
  margin: 0 auto 3rem auto;
  color: var(--kodekloud-text-light);
}

/* Additional KodeKloud Exact Match Styles */

/* KodeKloud Section Headings - Exact Match */
.kodekloud-section-heading {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--kodekloud-text);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.kodekloud-section-subheading {
  font-size: 1.125rem;
  color: var(--kodekloud-text-light);
  margin-bottom: 2.5rem;
  max-width: 700px;
  line-height: 1.6;
}

/* KodeKloud Button Styles - Exact Match */
.kodekloud-primary-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  background: linear-gradient(to right, var(--kodekloud-primary), var(--kodekloud-primary-dark));
  color: white;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: all 0.3s;
  box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.kodekloud-primary-button:hover {
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
  transform: translateY(-2px);
}

.kodekloud-secondary-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: white;
  color: var(--kodekloud-primary);
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: all 0.3s;
}

.kodekloud-secondary-button:hover {
  border-color: var(--kodekloud-primary);
  background-color: #f8fafc;
}

/* KodeKloud Hero Section - Exact Match */
.kodekloud-hero {
  background: linear-gradient(to right, var(--kodekloud-gradient-start), var(--kodekloud-gradient-middle), var(--kodekloud-gradient-end));
  min-height: 90vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.kodekloud-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0wIDBoNjB2NjBIMHoiLz48cGF0aCBkPSJNMzAgMzFhMSAxIDAgMTEtMi0uMDAxIDEgMSAwIDAxMiAuMDAxeiIgZmlsbD0icmdiYSgyNTUsIDI1NSwgMjU1LCAwLjAyKSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9nPjwvc3ZnPg==');
  opacity: 0.3;
}

/* KodeKloud Testimonial Card - Exact Match */
.kodekloud-testimonial-card {
  background-color: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--kodekloud-card-shadow);
  border: 1px solid #f1f5f9;
  position: relative;
}

.kodekloud-testimonial-card::before {
  content: '"';
  position: absolute;
  top: 1.5rem;
  left: 2rem;
  font-size: 4rem;
  line-height: 1;
  font-weight: 700;
  color: var(--kodekloud-primary);
  opacity: 0.15;
  font-family: Georgia, serif;
}

/* KodeKloud Instructor Card - Exact Match */
.kodekloud-instructor-card {
  background-color: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--kodekloud-card-shadow);
  border: 1px solid #f1f5f9;
  transition: transform 0.3s, box-shadow 0.3s;
}

.kodekloud-instructor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.kodekloud-instructor-image {
  width: 100%;
  height: 260px;
  object-fit: cover;
}

.kodekloud-instructor-content {
  padding: 1.5rem;
}

.kodekloud-instructor-name {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--kodekloud-text);
  margin-bottom: 0.25rem;
}

.kodekloud-instructor-role {
  font-size: 0.875rem;
  color: var(--kodekloud-primary);
  font-weight: 500;
  margin-bottom: 1rem;
}

.kodekloud-instructor-bio {
  font-size: 0.875rem;
  color: var(--kodekloud-text-light);
  line-height: 1.6;
}
