import React from "react";
import { motion } from "framer-motion";
import SQLDropdown from "./SQLDropdown";

const SQLIntroduction = ({ onBackToCourse }) => {
  const sqlTopics = {
    "Basic SQL Queries": [
      {
        title: "SELECT Statements",
        tags: ["Basics", "Queries", "Data Retrieval"],
        keyPoints: [
          "Learn to retrieve data with SELECT",
          "Filter results with WHERE clause"
        ]
      },
      {
        title: "Sorting and Limiting Results",
        tags: ["ORDER BY", "LIMIT", "OFFSET"],
        keyPoints: [
          "Sort query results with ORDER BY",
          "Limit number of returned rows"
        ]
      }
    ],
    "Joins and Relationships": [
      {
        title: "INNER JOIN Operations",
        tags: ["Joins", "Table Relationships", "Data Combination"],
        keyPoints: [
          "Combine rows from multiple tables",
          "Match records based on join conditions"
        ]
      },
      {
        title: "LEFT, RIGHT, and FULL JOINs",
        tags: ["Outer Joins", "NULL Handling", "Complete Datasets"],
        keyPoints: [
          "Include unmatched rows in query results",
          "Handle NULL values in joined tables"
        ]
      }
    ],
    "Aggregation and Grouping": [
      {
        title: "Aggregate Functions",
        tags: ["COUNT", "SUM", "AVG", "MIN", "MAX"],
        keyPoints: [
          "Calculate summary statistics",
          "Combine with GROUP BY for segmented analysis"
        ]
      },
      {
        title: "GROUP BY and HAVING",
        tags: ["Data Grouping", "Filtering Groups", "Summarization"],
        keyPoints: [
          "Group rows based on column values",
          "Filter groups with HAVING clause"
        ]
      }
    ],
    "Advanced SQL Features": [
      {
        title: "Subqueries and CTEs",
        tags: ["Nested Queries", "Common Table Expressions", "WITH Clause"],
        keyPoints: [
          "Write queries within queries",
          "Create temporary result sets with CTEs"
        ]
      },
      {
        title: "Window Functions",
        tags: ["OVER", "PARTITION BY", "ROW_NUMBER", "RANK"],
        keyPoints: [
          "Perform calculations across rows",
          "Create running totals and moving averages"
        ]
      }
    ]
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/70 to-indigo-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">SQL Fundamentals</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Master SQL Queries</h3>
          <p className="text-gray-300">
            Explore our comprehensive collection of SQL topics organized by category. Each section contains 
            carefully selected problems with key concepts and implementation details.
          </p>
        </div>
        
        {/* Dropdowns for each category */}
        <div className="space-y-4">
          {Object.entries(sqlTopics).map(([category, items], index) => (
            <motion.div 
              key={category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <SQLDropdown 
                title={category} 
                items={items} 
                defaultOpen={index === 0} // Open first dropdown by default
              />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SQLIntroduction;