import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  FaShieldAlt,
  FaLock,
  FaCheckCircle,
  FaMobile,
  FaEnvelope,
  FaArrowRight,
  FaHome,
  FaUserShield
} from "react-icons/fa";
import "./two-factor.css";

const Force2FA = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  const benefits = [
    {
      icon: <FaShieldAlt />,
      title: "Prevent unauthorized access",
      description: "Block hackers from accessing your account"
    },
    {
      icon: <FaLock />,
      title: "Secure your sensitive information",
      description: "Protect your personal and professional data"
    },
    {
      icon: <FaUserShield />,
      title: "Ensure only you can log in",
      description: "Add an extra verification step for security"
    }
  ];

  return (
    <div className="h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 flex items-center justify-center px-4 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute top-10 right-10 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-cyan-500/10 rounded-full blur-2xl"></div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 w-full max-w-sm"
      >
        {/* Main Card */}
        <div className="bg-gradient-to-br from-slate-800/95 via-slate-700/90 to-slate-800/95 backdrop-blur-xl rounded-2xl shadow-2xl p-6 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">

          {/* Header Section */}
          <motion.div variants={itemVariants} className="text-center mb-5">
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaShieldAlt className="text-2xl text-blue-400" />
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent">
                Secure Your Account
              </h1>
            </div>
            <p className="text-slate-300 text-sm">
              Enable 2-Factor Authentication
            </p>
          </motion.div>

          {/* Quick Benefits */}
          <motion.div variants={itemVariants} className="mb-5">
            <div className="grid grid-cols-3 gap-2">
              <div className="text-center p-3 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaShieldAlt className="text-green-400 text-lg mx-auto mb-1" />
                <p className="text-xs text-slate-300">Secure</p>
              </div>
              <div className="text-center p-3 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaLock className="text-blue-400 text-lg mx-auto mb-1" />
                <p className="text-xs text-slate-300">Protected</p>
              </div>
              <div className="text-center p-3 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaUserShield className="text-purple-400 text-lg mx-auto mb-1" />
                <p className="text-xs text-slate-300">Private</p>
              </div>
            </div>
          </motion.div>

          {/* Description */}
          <motion.div variants={itemVariants} className="mb-5">
            <div className="bg-gradient-to-r from-blue-900/40 to-purple-900/40 rounded-xl p-3 border border-blue-500/20">
              <p className="text-slate-200 text-center text-sm">
                Add an extra layer of security to protect your account.
              </p>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div variants={itemVariants} className="space-y-3">
            <Link to="/two-fa-email" className="block">
              <button className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-semibold flex items-center justify-center gap-2 group">
                <FaEnvelope className="text-sm" />
                <span>Enable 2FA Now</span>
                <FaArrowRight className="text-xs group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </Link>

            <Link to="/" className="block">
              <button className="w-full px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-medium text-sm flex items-center justify-center gap-2">
                <FaHome className="text-xs" />
                <span>Set Up Later</span>
              </button>
            </Link>
          </motion.div>

          {/* Footer Note */}
          <motion.div variants={itemVariants} className="mt-4 pt-3 border-t border-slate-600/30 text-center">
            <p className="text-slate-400 text-xs">
              Supports SMS, Email & Apps
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default Force2FA;
