import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const SidebarDropdown = ({ title, icon, options, basePath }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredItem, setHoveredItem] = useState(null);
  const location = useLocation();

  const toggleDropdown = (e) => {
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        onMouseEnter={() => setHoveredItem('dropdown-title')}
        onMouseLeave={() => setHoveredItem(null)}
        aria-expanded={isOpen}
        aria-controls={`${title.toLowerCase().replace(/\s+/g, '-')}-menu`}
        className={`
          w-full flex items-center justify-between px-3 py-2.5 rounded-lg transition-all duration-200 hover-translate
          ${location.pathname.startsWith(basePath) ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
        `}
      >
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-800/50">
            {icon}
          </div>
          <span className="text-white/90 font-medium">{title}</span>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <FaChevronDown className="text-white/70" />
        </motion.div>
      </button>
      
      {/* Dropdown Menu */}
      {isOpen && (
        <motion.div
          id={`${title.toLowerCase().replace(/\s+/g, '-')}-menu`}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="pl-10 mt-1 space-y-1"
          role="menu"
          aria-label={`${title} options`}
        >
          {options.map((option, index) => (
            <Link
              key={option.path}
              to={option.path}
              role="menuitem"
              onMouseEnter={() => setHoveredItem(`option-${index}`)}
              onMouseLeave={() => setHoveredItem(null)}
              className={`
                flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 hover-translate
                ${location.pathname === option.path ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
              `}
            >
              <div className="flex items-center justify-center w-6 h-6 rounded-lg bg-gray-800/50">
                {option.icon}
              </div>
              <span className="text-white/90 font-medium text-sm">{option.name}</span>
              {hoveredItem === `option-${index}` && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="ml-auto w-1.5 h-1.5 bg-blue-400 rounded-full"
                />
              )}
            </Link>
          ))}
        </motion.div>
      )}
    </div>
  );
};

export default SidebarDropdown;