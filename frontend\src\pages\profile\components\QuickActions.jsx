import { FaCode, FaChartLine, FaAward } from "react-icons/fa";

const QuickActions = () => {
  const actions = [
    {
      icon: FaCode,
      title: "New Project",
      subtitle: "Start a new project",
      gradient: "from-blue-500 to-cyan-500",
      hoverBorder: "hover:border-blue-500/50"
    },
    {
      icon: FaChartLine,
      title: "Analytics",
      subtitle: "View detailed stats",
      gradient: "from-purple-500 to-pink-500",
      hoverBorder: "hover:border-purple-500/50"
    },
    {
      icon: FaAward,
      title: "Achievements",
      subtitle: "View your milestones",
      gradient: "from-green-500 to-emerald-500",
      hoverBorder: "hover:border-green-500/50"
    }
  ];

  return (
    <div className="mt-8 bg-gradient-to-br from-indigo-900/60 via-purple-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-indigo-500/30 hover:border-purple-400/50 transition-all duration-500">
      <h3 className="text-xl font-bold text-indigo-200 mb-4 flex items-center gap-2">
        🚀 Quick Actions
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {actions.map((action, index) => {
          const IconComponent = action.icon;
          return (
            <button 
              key={index}
              className={`flex items-center gap-3 p-4 bg-slate-900/50 hover:bg-slate-800/50 rounded-xl transition-all duration-300 border border-slate-600/30 ${action.hoverBorder} group`}
            >
              <div className={`w-10 h-10 bg-gradient-to-r ${action.gradient} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                <IconComponent className="text-white" />
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">{action.title}</div>
                <div className="text-slate-400 text-sm">{action.subtitle}</div>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default QuickActions;
