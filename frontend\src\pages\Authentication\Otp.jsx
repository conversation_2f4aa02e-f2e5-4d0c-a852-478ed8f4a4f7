import  { useRef, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";

export default function OTPInput() {
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const inputsRef = useRef([]);
  const navigate = useNavigate();

  const handleChange = (element, index) => {
    const val = element.value;
    if (!/^\d*$/.test(val)) return;

    const newOtp = [...otp];
    newOtp[index] = val;
    setOtp(newOtp);

    if (val && index < 5) {
      inputsRef.current[index + 1].focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputsRef.current[index - 1].focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData("text").slice(0, 6);
    if (!/^\d+$/.test(pasteData)) return;

    const pasteArray = pasteData.split("");
    const newOtp = [...otp];
    for (let i = 0; i < 6; i++) {
      newOtp[i] = pasteArray[i] || "";
    }
    setOtp(newOtp);

    const focusIndex = pasteArray.length < 6 ? pasteArray.length : 5;
    inputsRef.current[focusIndex].focus();
  };

 const handleSubmit = async (e) => {
    e.preventDefault();
    if (otp.includes("")) {
      alert("Please enter all 6 digits of the OTP.");
      return;
    }

    const otpCode = otp.join("");

    try {
      const { data } = await axiosInstance.post(

        "/auth/activate-user",
        { activation_code: otpCode },
        { withCredentials: true }
      );

      if(data.success){
        toast.success(data.message)
        setOtp([])
        navigate("/login")
      }
    } catch (error) {
      toast.error(error.response?.data?.message);
    }
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-[#181c25] to-[#12171d] flex-col justify-center items-center bg-gray-100 px-4">
      <h2 className="text-4xl font-semibold mb-6 text-white">
        Enter 6-Digit OTP
      </h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex space-x-3">
          {otp.map((digit, index) => (
            <input
              key={index}
              type="text"
              inputMode="numeric"
              maxLength={1}
              className="w-12 h-12 text-center border border-gray-300 rounded-md text-lg font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={digit}
              onChange={(e) => handleChange(e.target, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              ref={(el) => (inputsRef.current[index] = el)}
              onPaste={handlePaste}
              autoComplete="one-time-code"
            />
          ))}
        </div>
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition"
        >
          Verify OTP
        </button>
      </form>
    </div>
  );
}
