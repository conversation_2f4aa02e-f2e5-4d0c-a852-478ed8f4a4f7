import "./App.css";
import { Home } from "./components/pages";
import { CodeEditor } from "./components/features";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import PythonCourse from "./pages/python/PythonCourse";
import FullstackCourse from "./pages/fullstack/FullstackCourse";
import PythonFullStack from "./pages/fullstack/python/PythonFullStack";
import JavaFullStack from "./pages/fullstack/java/JavaFullStack";
import DataScienceCourse from "./pages/datascience/DataScienceCourse";
// import TopInterviewQuestion from "./pages/InterviewQuestion/TopInterviewQuestion";
import GG_75 from "./pages/InterviewQuestion/GG_75";
// import SystemDesign from "./pages/system-design/SystemDesign";
import SystemDesignNew from "./pages/system-design/SystemDesignNew";
// import DSA from "./pages/DSA/DSA";
import DSA_Course from "./pages/DSA/DSA_Course";
// import SQL from "./pages/SQL/SQL";
import SQL_Course from "./pages/SQL/SQL_Course";
import Javascript from "./pages/javascript/Javascript";
import Interview150 from "./pages/Interview-150/Interview150";
import Interview30 from "./pages/Interview-30/Interview30";
import MLCourseWrapper from "./pages/ML/MLCourseWrapper";
import MyProfile from "./pages/profile/MyProfile";
import SignIn from "./pages/Authentication/SignUp";
import Login from "./pages/Authentication/Login";
import AICourseWrapper from "./pages/AI/AICourseWrapper";
import Codexuslab from "./pages/labsetup/Codexuslab";
import { CoursesPage } from "./pages/courses";
import ForgotPassword from "./pages/Authentication/ForgotPassword";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { Toaster } from "react-hot-toast";
import Verification from "./pages/Authentication/Verification";
import NewPassword from "./pages/Authentication/NewPassword";
import TwoFactor from "./pages/2FA/TwoFactor";
import TwoFactorEmail from "./pages/2FA/TwoFactorEmail";
import TwoFactorOtp from "./pages/2FA/TwoFacrorOtp";
import TwoFactorSuccess from "./pages/2FA/TwoFactorSuccess";
import AdminHome from "./admin/AdminHome";
import NotFound from "./pages/not-found/NotFound";
import InternshipRegistration from "./pages/InternshipRegistration/InternshipRegistration";
import Contests from "./pages/contests/Contests";
import AdminDashboard from "./pages/admin-dashboard/AdminDashboard";
import Otp from "./pages/Authentication/Otp";
import LabDetailsPage from "./pages/labDetails/LabDetailsPage";
import Community from "./pages/community/Community";


const App = () => {
  return (
    <Router>
      <Toaster position="top-center" reverseOrder={false} />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/courses" element={<CoursesPage />} />
        <Route path="/labs/:id" element={<LabDetailsPage />} />
        <Route path="/admin/*" element={<AdminHome />} />
        <Route path="/code-editor/:id/:mode" element={<CodeEditor />} />
        <Route path="/pythoncourse" element={<PythonCourse />} />
        <Route path="/fullstack-course" element={<FullstackCourse />} />
        <Route path="/fullstack/python" element={<PythonFullStack />} />
        <Route path="/fullstack/java" element={<JavaFullStack />} />
        <Route path="/datascience-course" element={<DataScienceCourse />} />
        <Route path="/ml-course" element={<MLCourseWrapper />} />
        <Route path="/ai-course" element={<AICourseWrapper />} />
        <Route path="/lab-problems/:id" element={<Codexuslab />} />
        <Route path="/profile/*" element={<MyProfile />} />
        <Route path="/GG_75" element={<GG_75 />} />
        <Route path="/sys_des_for_int" element={<SystemDesignNew />} />
        <Route path="/data_strut" element={<DSA_Course />} />
        <Route path="/sql_50" element={<SQL_Course />} />
        <Route path="/30_days_js" element={<Javascript />} />
        <Route path="/interview_150" element={<Interview150 />} />
        <Route
          path="/internship-registration"
          element={<InternshipRegistration />}
        />
        <Route path="/top_inter" element={<Interview30 />} />
        <Route path="/contests" element={<Contests />} />
        <Route path="/community" element={<Community />} />
        <Route path="/admin-dashboard" element={<AdminDashboard />} />
        <Route
          path="/SignUp"
          element={
            <GoogleOAuthProvider clientId="630144401971-5ftebubeqveglap0qg5ahahaefi4nd9u.apps.googleusercontent.com">
              <SignIn />
            </GoogleOAuthProvider>
          }
        />
        <Route
          path="/login"
          element={
            <GoogleOAuthProvider clientId="630144401971-5ftebubeqveglap0qg5ahahaefi4nd9u.apps.googleusercontent.com">
              <Login />
            </GoogleOAuthProvider>
          }
        />
        <Route path="/verification" element={<Verification />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password-page" element={<NewPassword />} />
        <Route path="/two-fa" element={<TwoFactor />} />
        <Route path="/two-fa-email" element={<TwoFactorEmail />} />
        <Route path="/two-fa-otp" element={<TwoFactorOtp />} />
        <Route path="/verify-2fa" element={<TwoFactorSuccess />} />
        <Route path="/otp" element={<Otp></Otp>} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
};

export default App;
