import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import ReusableNavbar from "./ReusableNavbar";
import SidebarWrapper from "./SidebarWrapper";
import MobileOverlay from "./MobileOverlay";
import { AnimatedBackground } from "../ui";

const CourseLayout = ({
  children,
  isSidebarOpen,
  toggleSidebar,
  title,
  showAnimatedBackground = true
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden"
    >
      {/* Background animated elements */}
      {showAnimatedBackground && <AnimatedBackground />}

      {/* Navbar */}
      <ReusableNavbar 
        toggleSidebar={toggleSidebar} 
        title={title}
        isSidebarOpen={isSidebarOpen}
      />
      
      {/* Mobile Overlay */}
      <MobileOverlay isOpen={isMobile && isSidebarOpen} onClose={toggleSidebar} />

      {/* Sidebar */}
      <SidebarWrapper isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      
      {/* Main Content with dynamic left margin for sidebar */}
      <motion.div
        className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative z-10 transition-all duration-400"
        style={{
          marginLeft: !isMobile && isSidebarOpen ? "280px" : "0px" // Only apply margin on desktop when sidebar is open
        }}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

export default CourseLayout;
