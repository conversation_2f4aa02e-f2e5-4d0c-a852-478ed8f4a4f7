import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    token: null,
    user: null,
};

const authSlice = createSlice({
    name: "auth",
    initialState,
    reducers: {
        userRegistration: (state, action) => {
            state.token = action.payload.token;
        },
        userLoggedIn: (state, action) => {
            
            state.token = action.payload.accessToken || state.token;
            state.user = {
                ...state.user,
                ...action.payload.user,
            };
           
        },
        userLoggedOut: (state) => {
            state.token = null;
            state.user = null;
        },
    },
});

export const { userRegistration, userLoggedOut, userLoggedIn } = authSlice.actions;

export default authSlice.reducer;
