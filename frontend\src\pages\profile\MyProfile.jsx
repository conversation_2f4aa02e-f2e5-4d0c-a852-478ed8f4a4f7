import "./profile.css";
import { useEffect, useState } from "react";
import axiosInstance from "../../utils/axiosInstance";
import { userLoggedIn } from "../../features/authSlice";
import { useDispatch } from "react-redux";
import { Routes, Route, Link } from "react-router-dom";
import { motion } from "framer-motion";
import { FaUser } from "react-icons/fa";
import ReusableNavbar from "../../components/layout/ReusableNavbar";

import { TbCashRegister } from "react-icons/tb";
import { MdOutlinePayment } from "react-icons/md";
import { IoMdAnalytics } from "react-icons/io";
import { FaFileInvoice } from "react-icons/fa";
import MyProfilePage from "./MyProfilePage";
import EditProfilePage from "./EditProfilePage";
import SocialAccount from "./SocialAccount";
import ChangePassword from "./ChangePassword";
import ProjectStatus from "./ProjectStatus";
import ProjectTracker from "./ProjectTracker";
import DeleteAccount from "./DeleteAccount";

const MyProfile = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth <= 768) {
        setIsMobile(true);
        setIsSidebarOpen(false);
      } else {
        setIsMobile(false);
        setIsSidebarOpen(true);
      }
    };

    checkMobile();

    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data } = await axiosInstance.get("/auth/me", {
          withCredentials: true,
        });
        dispatch(
          userLoggedIn({
            accessToken: data.accessToken,
            user: data.user,
          })
        );
      } catch (error) {
        console.error(
          "Error fetching user:",
          error.response?.data || error.message
        );
      }
    };

    fetchUser();
  }, [dispatch]);

  const [darkMode, setDarkMode] = useState(false);

  return (
    <div className={`${darkMode ? "dark" : ""} flex min-h-screen flex-col`}>
      {/* Navbar */}
      <ReusableNavbar
        toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
        showSidebarToggle={true}
        isSidebarOpen={isSidebarOpen}
        title="Profile Dashboard"
      />

      <div className="relative w-full">
        {/* Sidebar */}
        <motion.aside
          className="bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white w-64 p-4 space-y-6 fixed border-r border-slate-700/50 backdrop-blur-xl overflow-y-auto"
          animate={{ x: isMobile ? (isSidebarOpen ? 0 : -280) : 0 }}
          style={{
            position: "fixed",
            top: "80px",
            left: isMobile ? (isSidebarOpen ? 0 : -280) : 0,
            height: "calc(100vh - 80px)", 
            width: "256px", 
            zIndex: 40,
          }}
        >
          <nav className="space-y-2 mt-4">
            <Link
              to="/profile/my-profile"
              className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-600/20 hover:to-purple-600/20 transition-all duration-300 border border-transparent hover:border-blue-500/30 group"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaUser className="mr-3 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
              <span className="font-medium group-hover:text-white">
                My Profile
              </span>
            </Link>

            <Link
              to="/profile/edit-profile"
              className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-green-600/20 hover:to-emerald-600/20 transition-all duration-300 border border-transparent hover:border-green-500/30 group"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
              <span className="font-medium group-hover:text-white">
                Edit Profile
              </span>
            </Link>
            <Link
              to="/profile/social-accounts"
              className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-purple-600/20 hover:to-pink-600/20 transition-all duration-300 border border-transparent hover:border-purple-500/30 group"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />
              <span className="font-medium group-hover:text-white">
                Social Accounts
              </span>
            </Link>
            <Link
              to="/profile/change-password"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <MdOutlinePayment className="mr-3" /> Change Password
            </Link>
            <Link
              to="/profile/project-status"
              className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-cyan-600/20 hover:to-blue-600/20 transition-all duration-300 border border-transparent hover:border-cyan-500/30 group"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <IoMdAnalytics className="mr-3 text-cyan-400 group-hover:text-cyan-300 transition-colors duration-300" />
              <span className="font-medium group-hover:text-white">
                Project Status
              </span>
            </Link>
            <Link
              to="/profile/project-tracker"
              className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-indigo-600/20 hover:to-purple-600/20 transition-all duration-300 border border-transparent hover:border-indigo-500/30 group"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <TbCashRegister className="mr-3 text-indigo-400 group-hover:text-indigo-300 transition-colors duration-300" />
              <span className="font-medium group-hover:text-white">
                Project Tracker
              </span>
            </Link>
            <Link
              to="/profile/delete-account"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <FaFileInvoice className="mr-3" /> Delete Account
            </Link>
            {/* <Link
              to="/admin/notifications"
              className="flex items-center p-2 rounded hover:bg-gray-700"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >-
              <IoIosNotifications className="mr-3" /> Notifications
            </Link> */}
          </nav>
        </motion.aside>

        {/* Content Area */}
        <div
          className={`bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 text-white transition-all duration-300 fixed z-0`}
          style={{
            left: isMobile ? 0 : "256px", 
            top: "80px",
            right: 0,
            bottom: 0,
            width: isMobile ? "100%" : "calc(100% - 256px)", 
            height: "calc(100vh - 80px)", 
            overflow: "auto",
            padding: "0px", 
            margin: "0px",
          }}
        >
          <Routes>
            <Route path="my-profile" element={<MyProfilePage />} />
            <Route path="edit-profile" element={<EditProfilePage />} />
            <Route path="social-accounts" element={<SocialAccount />} />
            <Route path="change-password" element={<ChangePassword />} />
            <Route path="project-status" element={<ProjectStatus />} />
            <Route path="project-tracker" element={<ProjectTracker />} />
            <Route path="delete-account" element={<DeleteAccount />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default MyProfile;
