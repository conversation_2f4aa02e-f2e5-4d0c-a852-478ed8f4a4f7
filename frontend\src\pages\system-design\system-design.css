
:root {
    --primary: #0284c7;
    --secondary: #64748b;
    --success: #059669;
    --warning: #d97706;
    --danger: #dc2626;
    --bg-light: #f8fafc;
    --border: #e2e8f0;
}

.main-div {
    background: var(--bg-light);
    font-family: system-ui, -apple-system, sans-serif;
    color: #1e293b;
}

.container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.header-system {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    border-radius: 12px;
}

.header-system h1 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.5rem;
    color: var(--primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.difficulty-badge {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    color: white;
}

.difficulty-easy {
    background: var(--success);
}

.difficulty-medium {
    background: var(--warning);
}

.difficulty-hard {
    background: var(--danger);
}

.question-card {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.question-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.question-title {
    font-weight: 600;
    color: var(--primary);
    margin: 0;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.tag {
    background: white;
    color: var(--secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: 0.875rem;
    border: 1px solid var(--border);
}

.key-points {
    margin-top: 1rem;
    padding-left: 1.5rem;
    color: var(--secondary);
}

.key-points li {
    margin-bottom: 0.5rem;
}

.checklist {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}

.checklist li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--secondary);
}

.checklist li i {
    color: var(--success);
}

@media (max-width: 768px) {
    .container {
        margin: 1rem auto;
    }

    .question-header {
        flex-direction: column;
    }
}

.nav-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.nav-box {
    flex: 1;
    background-color: #fff;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 0 0.5rem;
}

.nav-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.nav-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

@media (max-width: 600px) {
    .nav-container {
        flex-direction: column;
        align-items: center;
    }

    .nav-box {
        margin: 0.5rem 0;
        width: 100%;
    }
}

.question {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 8px 0;
    font-weight: 600;
}

.answer {
    
    padding-left: 24px;
    color: #555;
}

.toggle-icon {
    margin-left: auto;
    font-size: 1.2em;
    color: #007bff;
    cursor: pointer;
}

ul {
    list-style-type: none;
    padding: 0;
}

.question {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 16px 0;
}

.answer {
    padding-left: 24px;
    color: #555;
}

.toggle-icon {
    margin-left: auto;
    font-size: 1.2em;
    color: #007bff;
    cursor: pointer;
}

.spacer {
    margin-top: 3rem;
}

@media (max-width: 767px) {
    .display-4 {
        font-size: 1.2rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}

@media (min-width: 768px) {
    .display-4 {
        font-size: 2rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}
@media (min-width: 1024px) {
    .display-4 {
        font-size: 2.5rem;
        margin-bottom: 5rem;
    }
    .spacer {
        margin-top: 5rem;
    }
}