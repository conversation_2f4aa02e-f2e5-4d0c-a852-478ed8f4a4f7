import React, { useState } from 'react';
import { <PERSON>aSave, FaBell, <PERSON>a<PERSON>ock, Fa<PERSON>ser, FaCog, FaPalette } from 'react-icons/fa';

const SettingsSection = () => {
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    { id: 'general', label: 'General', icon: <FaCog /> },
    { id: 'profile', label: 'Profile', icon: <FaUser /> },
    { id: 'security', label: 'Security', icon: <FaLock /> },
    { id: 'notifications', label: 'Notifications', icon: <FaBell /> },
    { id: 'appearance', label: 'Appearance', icon: <FaPalette /> }
  ];

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Settings</h2>
        <p className="text-gray-600">Manage your account settings and preferences</p>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`flex items-center px-4 py-3 text-sm font-medium ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        <div className="p-6">
          {activeTab === 'general' && (
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">General Settings</h3>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Platform Name
                  </label>
                  <input
                    type="text"
                    className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="Codexus Learning Platform"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Language
                  </label>
                  <select className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option>English</option>
                    <option>Spanish</option>
                    <option>French</option>
                    <option>German</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Time Zone
                  </label>
                  <select className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option>UTC (Coordinated Universal Time)</option>
                    <option>EST (Eastern Standard Time)</option>
                    <option>CST (Central Standard Time)</option>
                    <option>PST (Pacific Standard Time)</option>
                  </select>
                </div>
                
                <div className="flex items-center justify-end">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
                    <FaSave className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'profile' && (
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">Profile Settings</h3>
              <p className="text-gray-600">Update your profile information</p>
            </div>
          )}
          
          {activeTab === 'security' && (
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">Security Settings</h3>
              <p className="text-gray-600">Manage your account security</p>
            </div>
          )}
          
          {activeTab === 'notifications' && (
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">Notification Settings</h3>
              <p className="text-gray-600">Configure your notification preferences</p>
            </div>
          )}
          
          {activeTab === 'appearance' && (
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">Appearance Settings</h3>
              <p className="text-gray-600">Customize the look and feel of the platform</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsSection;