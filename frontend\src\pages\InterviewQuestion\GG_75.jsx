import React, { useState } from "react";
import { CourseResourcesSection } from "../../components/ui";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import InterviewHeroNew from "./components/InterviewHeroNew";
import InterviewLayout from "./components/InterviewLayout";
import InterviewPremiumModal from "./components/InterviewPremiumModal";
import QuestionSectionNew from "./components/QuestionSectionNew";
import InterviewChecklistNew from "./components/InterviewChecklistNew";
import useInterviewData from "./hooks/useInterviewData";

const GG_75 = () => {
  const { interviewData, interviewChecklist } = useInterviewData();

  const courseConfig = {
    title: "Interview Questions Resources",
    subtitle: "Select a resource category to start practicing interview questions",
    theme: {
      titleColor: "text-gray-100",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Questions",
        title: "Interview Questions",
        description: "Practice top interview questions",
        icon: "📝",
        component: ({ showPremiumOverlay }) => (
          <QuestionSectionNew interviewData={interviewData} showPremiumOverlay={showPremiumOverlay} />
        ),
        props: {}
      },
      {
        id: "Checklist",
        title: "Interview Checklist",
        description: "Prepare with our interview checklist",
        icon: "✅",
        component: ({ showPremiumOverlay }) => (
          <InterviewChecklistNew interviewChecklist={interviewChecklist} showPremiumOverlay={showPremiumOverlay} />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="gg75-course"
            courseName="GG 75 Interview Questions"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={InterviewHeroNew}
      LayoutComponent={InterviewLayout}
      PremiumModalComponent={InterviewPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default GG_75;