import { useState } from "react";
import TopicGrid from "./components/case-studies/TopicGrid";
import CaseStudySection from "./components/case-studies/CaseStudySection";
import { 
  topics,
  supervisedLearningStudies,
  unsupervisedLearningStudies,
  reinforcementLearningStudies,
  deepLearningStudies,
  nlpStudies,
  featureEngineeringStudies,
  modelEvaluationStudies,
  ensembleLearningStudies,
  dimensionalityReductionStudies,
  timeSeriesStudies
} from "./components/case-studies/data/caseStudiesData";

const CaseStudy = () => {
  // State for each section
  const [activeIndex, setActiveIndex] = useState(null);
  const [showSolutions, setShowSolutions] = useState(Array(supervisedLearningStudies.length).fill(false));
  
  // State for other sections
  const [activeStudy, setActiveStudy] = useState(null);
  const [showSolution, setShowSolution] = useState(Array(10).fill(false));
  
  // State for expanded sections
  const [expandedSections, setExpandedSections] = useState({});

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      // Expand the section when scrolled to
      const sectionTitle = topics.find(topic => topic.sectionId === sectionId)?.title;
      if (sectionTitle) {
        setExpandedSections(prev => ({
          ...prev,
          [sectionTitle]: true
        }));
      }
    }
  };

  const toggleSolution = (index) => {
    const updatedSolutions = [...showSolutions];
    updatedSolutions[index] = !updatedSolutions[index];
    setShowSolutions(updatedSolutions);
  };

  const toggleChapterContent = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const toggleContent = (index) => {
    setActiveStudy(activeStudy === index ? null : index);
  };

  const toggleSolutionTwo = (index) => {
    const updatedSolutions = [...showSolution];
    updatedSolutions[index] = !updatedSolutions[index];
    setShowSolution(updatedSolutions);
  };
  
  const toggleSection = (sectionTitle) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  return (
    <div className="bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900">
      {/* Topic Grid */}
      <div className="bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 py-4">
        <div className="container mx-auto px-4">
          <div className="text-center mb-4">
            <h1 className="text-2xl font-bold text-white mb-1">Machine Learning Case Studies</h1>
            <p className="text-gray-300 text-sm max-w-2xl mx-auto">
              Explore practical case studies across different machine learning domains
            </p>
          </div>
          
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white mb-2">Select a Topic</h2>
            <TopicGrid topics={topics} scrollToSection={scrollToSection} />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pb-8 space-y-2">
        {/* Supervised Learning */}
        <CaseStudySection
          title="Supervised Learning"
          studies={supervisedLearningStudies}
          activeIndex={activeIndex}
          showSolutions={showSolutions}
          toggleContent={toggleChapterContent}
          toggleSolution={toggleSolution}
          isExpanded={expandedSections["Supervised Learning"]}
          toggleSection={toggleSection}
        />

        {/* Unsupervised Learning */}
        <CaseStudySection
          title="Unsupervised Learning"
          studies={unsupervisedLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Unsupervised Learning"]}
          toggleSection={toggleSection}
        />

        {/* Reinforcement Learning */}
        <CaseStudySection
          title="Reinforcement Learning"
          studies={reinforcementLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Reinforcement Learning"]}
          toggleSection={toggleSection}
        />

        {/* Deep Learning */}
        <CaseStudySection
          title="Deep Learning"
          studies={deepLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Deep Learning"]}
          toggleSection={toggleSection}
        />

        {/* NLP */}
        <CaseStudySection
          title="Natural Language Processing (NLP)"
          studies={nlpStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Natural Language Processing (NLP)"]}
          toggleSection={toggleSection}
        />

        {/* Feature Engineering */}
        <CaseStudySection
          title="Feature Engineering"
          studies={featureEngineeringStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Feature Engineering"]}
          toggleSection={toggleSection}
        />

        {/* Model Evaluation */}
        <CaseStudySection
          title="Model Evaluation and Validation"
          studies={modelEvaluationStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Model Evaluation and Validation"]}
          toggleSection={toggleSection}
        />

        {/* Ensemble Learning */}
        <CaseStudySection
          title="Ensemble Learning"
          studies={ensembleLearningStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Ensemble Learning"]}
          toggleSection={toggleSection}
        />

        {/* Dimensionality Reduction */}
        <CaseStudySection
          title="Dimensionality Reduction"
          studies={dimensionalityReductionStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Dimensionality Reduction"]}
          toggleSection={toggleSection}
        />

        {/* Time Series */}
        <CaseStudySection
          title="Time Series Analysis and Forecasting"
          studies={timeSeriesStudies}
          activeIndex={activeStudy}
          showSolutions={showSolution}
          toggleContent={toggleContent}
          toggleSolution={toggleSolutionTwo}
          isExpanded={expandedSections["Time Series Analysis and Forecasting"]}
          toggleSection={toggleSection}
        />
      </div>
    </div>
  );
};

export default CaseStudy;