import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Section from "./Section";
import axiosInstance from "../../utils/axiosInstance";

const LabDetailsPage = ({ showPremiumOverlay }) => {
  const { id } = useParams();
  console.log(id);
  const [labPage, setLabPage] = useState({});
  useEffect(() => {
    const fetchLabDetails = async () => {
      const { data } = await axiosInstance.get(
        `/lab/${id}`,
      );
      if (data.success) {
        setLabPage(data.lab);
      }
    };
    fetchLabDetails();
  }, [id]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white relative overflow-hidden">
      {/* Background animated elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-full blur-3xl opacity-50 animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl opacity-50 animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto px-4 md:px-6 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <div className="text-center lg:text-left">
              <div className="inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
                🧠 {labPage?.name}
              </div>

              {labPage?.title && (
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                  {labPage.title.startsWith("Master") ? (
                    <>
                      Master{" "}
                      <span className="bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                        {labPage.title.replace("Master ", "")}
                      </span>
                    </>
                  ) : (
                    <span className="bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                      {labPage.title}
                    </span>
                  )}
                </h1>
              )}

              <p className="text-xl text-blue-100/80 max-w-2xl mx-auto lg:mx-0 leading-relaxed mb-8">
                {labPage?.description}
              </p>

              <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-8">
                <button
                  onClick={showPremiumOverlay}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <span className="mr-2">🚀</span>
                  Start Learning
                  <span className="ml-2">→</span>
                </button>

                <button className="inline-flex items-center px-8 py-4 border border-white/20 text-white font-semibold rounded-2xl hover:bg-white/10 transition-all duration-300">
                  <span className="mr-2">📚</span>
                  View Curriculum
                </button>
              </div>

              {/* Stats */}
              <div className="flex flex-wrap gap-6 justify-center lg:justify-start text-sm">
                <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                  <span className="text-purple-400">🎯</span>
                  <span className="text-white/80">50+ ML Algorithms</span>
                </div>
                <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                  <span className="text-indigo-400">💼</span>
                  <span className="text-white/80">Real Projects</span>
                </div>
                <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                  <span className="text-purple-400">⭐</span>
                  <span className="text-white/80">Industry Ready</span>
                </div>
              </div>
            </div>

            {/* Right Column - Visual Element */}
            <div className="relative">
              <div className="bg-gradient-to-br from-purple-900/20 to-indigo-900/20 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-2xl">
                <div className="text-center mb-6">
                  <div className="text-6xl mb-4">🧠</div>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {labPage?.name}
                  </h3>
                  {/* <p className="text-blue-100/70">Deep Learning & AI</p> */}
                </div>

                <div className="space-y-4">
                  {labPage?.learningPoints?.map((topic, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                    >
                      <span className="text-white/90">{topic}</span>
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-full"></div>
                    </div>
                  ))}
                </div>

                <button
                  onClick={showPremiumOverlay}
                  className="w-full mt-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300"
                >
                  Explore Curriculum
                </button>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center text-2xl shadow-lg transform rotate-12">
                🤖
              </div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center text-xl shadow-lg transform -rotate-12">
                📊
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <Section labPage={labPage}/>
      </div>
    </div>
    
  );
};

export default LabDetailsPage;
