import React from "react";
import { motion } from "framer-motion";
import { DSADropdown } from "./index";
import useDSAData from "../hooks/useDSAData";

const DSAIntroduction = ({ onBackToCourse }) => {
  const { dsaData } = useDSAData();
  
  // Group questions by category for dropdown structure
  const getGroupedQuestions = () => {
    const categories = {
      "Arrays & Strings": [],
      "Linked Lists": [],
      "Trees & Graphs": [],
      "Sorting & Searching": [],
      "Dynamic Programming": []
    };
    
    // Distribute questions into categories based on tags
    dsaData.forEach(section => {
      section.questions.forEach(question => {
        const tags = question.tags.map(tag => tag.toLowerCase());
        
        if (tags.some(tag => tag.includes('array') || tag.includes('string'))) {
          categories["Arrays & Strings"].push({...question, difficulty: section.difficulty});
        } else if (tags.some(tag => tag.includes('linked list'))) {
          categories["Linked Lists"].push({...question, difficulty: section.difficulty});
        } else if (tags.some(tag => tag.includes('tree') || tag.includes('graph'))) {
          categories["Trees & Graphs"].push({...question, difficulty: section.difficulty});
        } else if (tags.some(tag => tag.includes('sort') || tag.includes('search'))) {
          categories["Sorting & Searching"].push({...question, difficulty: section.difficulty});
        } else if (tags.some(tag => tag.includes('dynamic'))) {
          categories["Dynamic Programming"].push({...question, difficulty: section.difficulty});
        } else {
          // Default category
          categories["Arrays & Strings"].push({...question, difficulty: section.difficulty});
        }
      });
    });
    
    return categories;
  };
  
  const groupedQuestions = getGroupedQuestions();

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/70 to-indigo-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">DSA Fundamentals</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Master Data Structures & Algorithms</h3>
          <p className="text-gray-300">
            Explore our comprehensive collection of DSA topics organized by category. Each section contains 
            carefully selected problems with key concepts and implementation details.
          </p>
        </div>
        
        {/* Dropdowns for each category */}
        <div className="space-y-4">
          {Object.entries(groupedQuestions).map(([category, items], index) => (
            items.length > 0 && (
              <motion.div 
                key={category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <DSADropdown 
                  title={category} 
                  items={items} 
                  defaultOpen={index === 0} // Open first dropdown by default
                />
              </motion.div>
            )
          ))}
        </div>
      </div>
    </div>
  );
};

export default DSAIntroduction;