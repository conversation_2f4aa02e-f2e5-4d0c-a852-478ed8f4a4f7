import { useEffect, useState } from "react";
import { DeleteModal } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";
import toast from "react-hot-toast";

const Internship = () => {
  const [registrations, setRegistrations] = useState([]);
  const [selectedUserId, setSelectedUserId] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);
  const casesPerPage = 5;

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const { data } = await axiosInstance.get(
          "http://localhost:8000/api/v1/get-internship",
          { withCredentials: true }
        );
        setRegistrations(data.registrations);
      } catch (err) {
        console.log(err.response?.data?.message || "Something went wrong!");
      }
    };

    fetchUsers();
  }, []);

  const indexOfLastCase = currentPage * casesPerPage;
  const indexOfFirstCase = indexOfLastCase - casesPerPage;
  const currentCases = registrations.slice(indexOfFirstCase, indexOfLastCase);

  const nextPage = () => {
    if (currentPage < Math.ceil(registrations.length / casesPerPage)) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const openDeleteModal = (userId) => {
    setSelectedUserId(userId);
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setSelectedUserId(null);
    setIsOpen(false);
  };

  const deleteConfirmation = async () => {
    if (!selectedUserId) {
      toast.error("ID is missing!");
      return;
    }

    try {
      const { data } = await axiosInstance.delete(
        `http://localhost:8000/api/v1/delete-internship/${selectedUserId}`,
        {
          withCredentials: true,
        }
      );

      if (data.success) {
        toast.success(data.message);
      } else {
        toast.error(data.error || "Error deleting user");
      }

      closeDeleteModal();
      window.location.reload();
    } catch (err) {
      toast.error(err?.response?.data?.message || "Error deleting user!");
    }
  };

  return (
    <div className="bg-gray-100 min-h-screen py-10">
      {/* Filter Section */}
      <div className="w-[95%] max-w-[1100px] mx-auto p-6 bg-white border border-gray-300 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-blue-500 mb-6">
          Filter Internships
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="program-name"
              className="block font-semibold text-gray-700 mb-2"
            >
              Program Name
            </label>
            <input
              type="text"
              id="program-name"
              placeholder="Enter Program Name"
              className="w-full h-10 px-3 border border-gray-300 rounded-md outline-none"
            />
          </div>

          <div>
            <label
              htmlFor="start-date"
              className="block font-semibold text-gray-700 mb-2"
            >
              Start Date
            </label>
            <input
              type="date"
              id="start-date"
              className="w-full h-10 px-3 border border-gray-300 rounded-md outline-none"
            />
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="w-[95%] max-w-[1100px] mx-auto p-6 mt-8 bg-white rounded-lg shadow-lg overflow-hidden">
        <h2 className="text-xl font-bold text-blue-500 mb-4">
          Internship Details
        </h2>

        <div className="max-h-[400px] overflow-x-auto overflow-y-auto">
          <table className="w-full border-collapse text-sm sm:text-base">
            <thead className="bg-blue-900 text-white sticky top-0 z-10">
              <tr className="uppercase text-left font-bold">
                <th className="p-3">#</th>
                <th className="p-3">First Name</th>
                <th className="p-3">Last Name</th>
                <th className="p-3">Email</th>
                <th className="p-3">Mobile</th>
                <th className="p-3">Program</th>
                <th className="p-3">Availability</th>
                <th className="p-3">Skills</th>
                <th className="p-3">Description</th>
                <th className="p-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCases.map((data, index) => (
                <tr
                  key={index}
                  className="even:bg-gray-50 border-b border-gray-200 text-gray-800"
                >
                  <td className="p-3">{indexOfFirstCase + index + 1}</td>
                  <td className="p-3">{data.firstname}</td>
                  <td className="p-3">{data.lastname}</td>
                  <td className="p-3">{data.email}</td>
                  <td className="p-3">{data.mobileNumber}</td>
                  <td className="p-3">{data.internshipField}</td>
                  <td className="p-3">{data.availability}</td>
                  <td className="p-3">{data.skills}</td>
                  <td className="p-3">{data.projectDescription}</td>
                  <td className="p-3 text-center">
                    <button
                      className="w-[90px] h-[36px] bg-red-500 text-white rounded-md font-bold text-sm transition-transform transform hover:scale-105 hover:bg-red-600"
                      onClick={() => openDeleteModal(data._id)}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex flex-wrap justify-center items-center text-center mt-6 gap-3 sm:gap-0 sm:flex-nowrap">
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ⬅ Prev
          </button>
          <span className="text-lg font-bold text-blue-500 bg-blue-100 px-4 py-2 rounded-md">
            Page {currentPage}
          </span>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={nextPage}
            disabled={
              currentPage >= Math.ceil(registrations.length / casesPerPage)
            }
          >
            Next ➡
          </button>
        </div>
      </div>

      {/* Delete Modal */}
      <DeleteModal
        title={"Delete Internship Table Data"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />
    </div>
  );
};

export default Internship;
