import React from "react";
import { motion } from "framer-motion";
import PythonFullStackDropdown from "./PythonFullStackDropdown";

const PythonFullStackIntroduction = ({ onBackToCourse }) => {
  const pythonFullStackTopics = {
    "Python Backend Development": [
      {
        title: "Flask Framework",
        tags: ["Web Framework", "Lightweight", "Flexible"],
        keyPoints: [
          "Build RESTful APIs with Flask",
          "Create dynamic web applications"
        ]
      },
      {
        title: "Django Framework",
        tags: ["Full-featured", "ORM", "Admin Panel"],
        keyPoints: [
          "Develop complex web applications",
          "Utilize Django's built-in features"
        ]
      }
    ],
    "Frontend Integration": [
      {
        title: "Template Engines",
        tags: ["Jinja2", "Django Templates", "Rendering"],
        keyPoints: [
          "Create dynamic HTML with template engines",
          "Pass data from backend to frontend"
        ]
      },
      {
        title: "API Integration",
        tags: ["REST", "JSON", "Fetch API"],
        keyPoints: [
          "Connect Python backend with JavaScript frontend",
          "Build RESTful services for frontend consumption"
        ]
      }
    ],
    "Database Management": [
      {
        title: "SQL with Python",
        tags: ["SQLAlchemy", "PostgreSQL", "MySQL"],
        keyPoints: [
          "Design efficient database schemas",
          "Write optimized database queries"
        ]
      },
      {
        title: "ORM Techniques",
        tags: ["Object-Relational Mapping", "Models", "Migrations"],
        keyPoints: [
          "Map Python objects to database tables",
          "Manage database changes with migrations"
        ]
      }
    ],
    "Deployment & DevOps": [
      {
        title: "Containerization",
        tags: ["Docker", "Docker Compose", "Microservices"],
        keyPoints: [
          "Package applications with Docker",
          "Manage multi-container applications"
        ]
      },
      {
        title: "Cloud Deployment",
        tags: ["AWS", "Heroku", "CI/CD"],
        keyPoints: [
          "Deploy Python applications to cloud platforms",
          "Set up continuous integration pipelines"
        ]
      }
    ]
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Python Full Stack Development</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Master Full Stack Development with Python</h3>
          <p className="text-gray-300">
            Explore our comprehensive collection of Python full stack development topics. Learn how to build 
            complete web applications from frontend to backend using Python and modern web frameworks.
          </p>
        </div>
        
        {/* Dropdowns for each category */}
        <div className="space-y-4">
          {Object.entries(pythonFullStackTopics).map(([category, items], index) => (
            <motion.div 
              key={category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <PythonFullStackDropdown 
                title={category} 
                items={items} 
                defaultOpen={index === 0} // Open first dropdown by default
              />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PythonFullStackIntroduction;