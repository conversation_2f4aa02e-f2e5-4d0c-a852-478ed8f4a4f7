import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Navigation<PERSON>uttons, 
  QuestionS<PERSON>tion, 
  InterviewChecklist 
} from "./components";
import { Footer, CourseLayout } from "../../components/layout";
import useInterviewData from "./hooks/useInterviewData";
import useSidebarState from "../../hooks/useSidebarState";

const TopInterviewQuestion = () => {
  const { interviewData, interviewChecklist } = useInterviewData();
  const { isSidebarOpen, toggleSidebar } = useSidebarState(true); // Default to open
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Handle scroll to top visibility
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // CSS animations instead of framer-motion
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .main-content {
      opacity: 0;
      animation: fadeIn 0.5s forwards;
    }
    .item-animate {
      opacity: 0;
      animation: slideUp 0.5s forwards;
    }
    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.2s; }
    .delay-3 { animation-delay: 0.3s; }
    .delay-4 { animation-delay: 0.4s; }
  `;

  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="Interview Questions"
    >
      {/* Add inline styles for animations */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white min-h-screen flex items-center px-6 item-animate delay-1">
        <InterviewHero />
      </section>

      {/* Main Content */}
      <div className="flex-1">
        {/* Navigation Buttons */}
        <div className="py-8 item-animate delay-2">
          <div className="max-w-6xl mx-auto px-4">
            <NavigationButtons />
          </div>
        </div>
        
        {/* Interview Questions */}
        <div className="py-8 item-animate delay-3">
          <div className="max-w-6xl mx-auto px-4">
            <QuestionSection interviewData={interviewData} />
          </div>
        </div>
        
        {/* Interview Checklist */}
        <div className="py-8 item-animate delay-4">
          <div className="max-w-6xl mx-auto px-4">
            <InterviewChecklist interviewChecklist={interviewChecklist} />
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />

      {/* Floating Back to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 bg-[#656a9d] hover:bg-[#252035] text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-105 animate-fadeIn"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
            <path fillRule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z"/>
          </svg>
        </button>
      )}
    </CourseLayout>
  );
};

export default TopInterviewQuestion;
