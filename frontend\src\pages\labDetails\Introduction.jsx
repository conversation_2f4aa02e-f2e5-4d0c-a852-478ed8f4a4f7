import { useEffect } from "react";
import { useState } from "react";
import axiosInstance from "../../utils/axiosInstance";

const Introduction = ({
  showPremiumOverlay,
  onBackToCourse,
  labPage,
  sectionCardId,
}) => {
  const [showMore, setShowMore] = useState(false);
  const [section, setSection] = useState({});

  const labId = labPage._id;

  const toggleReadMore = () => {
    setShowMore(!showMore);
  };

  console.log(sectionCardId);
  const animationStyles = `
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes scaleIn {
          from { transform: scale(0.95); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }
        
        .animate-scaleIn {
          animation: scaleIn 0.5s ease-out forwards;
        }
        
        .hover-scale {
          transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
          transform: scale(1.05);
        }
      `;

  useEffect(() => {
    const fetchSectionDetails = async () => {
      try {
        const { data } = await axiosInstance.get(
          `/introduction/${labId}/${sectionCardId}`,
          { withCredentials: true }
        );
        if (data.success) {
          setSection(data.data);
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };

    if (sectionCardId) {
      fetchSectionDetails();
    }
  }, [sectionCardId, labId]);

  console.log(section);

  return (
    <div className="py-8">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />

      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-16 animate-fadeIn">
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🤖 {labPage?.name}
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              {labPage?.name}
            </span>
          </h2>

          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            {labPage?.description}
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Left Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🎯</span>
                Course Overview
              </h3>
              <p className="text-white/80 leading-relaxed">
                {section?.courseOverview}
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">💡</span>
                Why Machine Learning?
              </h3>
              <p className="text-white/80 leading-relaxed">
                {section?.whyLearning}
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🚀</span>
                What {"You'll"} Learn
              </h3>
              <p className="text-white/80 leading-relaxed">
                {section?.whatYouWillLearn}
              </p>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">📊</span>
                Key Topics
              </h3>

              <ul className="space-y-3 text-white/80">
                {section?.keyTopics?.map((topic, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    {topic}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🔧</span>
                Tools & Technologies
              </h3>

              <div className="grid grid-cols-2 gap-4">
                {section?.toolsAndTechnologies?.map((tool, index) => (
                  <div
                    key={index}
                    className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"
                  >
                    <span className="text-2xl mb-2 block">
                      {tool.icon.secure_url}
                    </span>
                    <span className="text-white/80 text-sm">{tool.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Content Section */}
        {showMore && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 animate-scaleIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🏆</span>
                Advanced Concepts
              </h3>
              <p className="text-white/80 leading-relaxed mb-4">
                {section?.conceptAndSkills?.description}
              </p>
              <ul className="space-y-2 text-white/80">
                {section?.conceptAndSkills?.points.map((point, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    {point}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">💼</span>
                Career Impact
              </h3>
              <p className="text-white/80 leading-relaxed">
                {section?.careerImpact}
              </p>
            </div>
          </div>
        )}

        {/* Read More Button */}
        <div className="text-center mb-12">
          <button
            onClick={toggleReadMore}
            className="bg-white/10 backdrop-blur-lg border border-white/20 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:bg-white/20"
          >
            {showMore ? "Show Less" : "Read More"}
          </button>
        </div>

        {/* Call to Action */}
        <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-12 animate-scaleIn">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Start Your ML Journey?
            </h3>
            <p className="text-white/80 text-lg mb-6 max-w-2xl mx-auto">
              Join thousands of students who have successfully launched their
              machine learning careers with our comprehensive program.
            </p>
            <button
              onClick={showPremiumOverlay}
              className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover-scale"
            >
              Get Premium Access →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Introduction;
