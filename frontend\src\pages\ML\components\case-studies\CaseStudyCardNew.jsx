import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const CaseStudyCardNew = ({ study, index, isActive, showSolution, toggleContent, toggleSolution }) => {
  return (
    <motion.li 
      className="group bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden hover:border-white/20 hover:shadow-xl transition-all duration-300 mb-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <motion.div
        className="p-4 cursor-pointer hover:bg-gradient-to-r hover:from-white/5 hover:to-blue-500/10 transition-all duration-200 flex items-center justify-between"
        onClick={() => toggleContent(index)}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-center space-x-4">
          <motion.div 
            className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300"
            whileHover={{ rotate: 5 }}
          >
            {index + 1}
          </motion.div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-blue-300 transition-colors duration-200" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
              {study.title}
            </h3>
            <p className="text-blue-100/60 text-sm">
              Click to explore this case study
            </p>
          </div>
        </div>
        
        <motion.div
          animate={{ rotate: isActive ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="text-2xl text-blue-400 group-hover:text-purple-400 transition-colors duration-200"
        >
          ↓
        </motion.div>
      </motion.div>

      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="overflow-hidden"
          >
            <div className="p-6 bg-gradient-to-b from-[#1e293b]/40 to-[#0d1117]/80 backdrop-blur-sm border-t border-white/10">
              <div className="grid lg:grid-cols-2 gap-6">
                {/* Left Column - Details */}
                <div className="space-y-4">
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                    className="bg-gradient-to-b from-[#1e293b]/30 to-[#0d1117]/80 backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-sm"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-xl">🎯</span>
                      <h4 className="font-semibold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Objective</h4>
                    </div>
                    <p className="text-blue-100/80 leading-relaxed text-sm">{study.objective}</p>
                  </motion.div>
                  
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="bg-gradient-to-b from-[#1e293b]/30 to-[#0d1117]/80 backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-sm"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-xl">📝</span>
                      <h4 className="font-semibold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Scenario</h4>
                    </div>
                    <p className="text-blue-100/80 leading-relaxed text-sm">{study.scenario}</p>
                  </motion.div>
                  
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="bg-gradient-to-b from-[#1e293b]/30 to-[#0d1117]/80 backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-sm"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-xl">🔑</span>
                      <h4 className="font-semibold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Key Concepts</h4>
                    </div>
                    <div className="text-blue-100/80 leading-relaxed text-sm">
                      {Array.isArray(study.keyConcepts) ? (
                        <div className="flex flex-wrap gap-2">
                          {study.keyConcepts.map((concept, idx) => (
                            <span key={idx} className="px-2 py-1 bg-[#1e293b]/80 border border-white/10 rounded-full text-xs text-blue-300">
                              {concept}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p>{study.keyConcepts}</p>
                      )}
                    </div>
                  </motion.div>
                </div>
                
                {/* Right Column - Solution */}
                <div>
                  <motion.div
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="bg-gradient-to-b from-[#1e293b]/30 to-[#0d1117]/80 backdrop-blur-lg border border-white/10 p-4 rounded-xl shadow-sm h-full"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-xl">💻</span>
                        <h4 className="font-semibold text-white" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Solution</h4>
                      </div>
                      
                      <motion.button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleSolution(index);
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm"
                      >
                        <span className="mr-2">
                          {showSolution ? "🙈" : "👁️"}
                        </span>
                        {showSolution ? "Hide Code" : "Show Code"}
                      </motion.button>
                    </div>
                    
                    <AnimatePresence>
                      {showSolution ? (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                          className="bg-[#0d1117] rounded-xl p-4 overflow-x-auto max-h-80 overflow-y-auto relative"
                        >
                          <pre className="text-green-400 text-sm font-mono leading-relaxed">
                            {study.solution}
                          </pre>
                          <button 
                            onClick={() => navigator.clipboard.writeText(study.solution)}
                            className="absolute top-2 right-2 p-1 bg-[#303246]/60 hover:bg-[#303246]/80 rounded-md transition-colors border border-white/10"
                            title="Copy code"
                          >
                            <svg className="w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="bg-gradient-to-br from-[#1e293b]/40 to-[#0d1117]/80 backdrop-blur-sm rounded-xl p-8 text-center border-2 border-dashed border-blue-400/50"
                        >
                          <span className="text-4xl mb-3 block">🔒</span>
                          <p className="text-blue-100/80 font-medium text-sm">
                            Click "Show Code" to reveal the solution
                          </p>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                </div>
              </div>
              
              {/* Progress indicator */}
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.4, duration: 0.6 }}
                className="mt-4 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.li>
  );
};

export default CaseStudyCardNew;