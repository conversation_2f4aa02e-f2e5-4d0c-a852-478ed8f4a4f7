import { useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { motion } from "framer-motion";
import { FaHome, FaTrophy, FaUsers, FaPython, FaLaptopCode, FaChartBar, FaRobot, FaBrain, FaDatabase, FaCode, FaQuestion, FaNetworkWired, FaChevronDown, FaJava, FaPhp, FaNodeJs } from 'react-icons/fa';
import { BsFileEarmarkCode } from 'react-icons/bs';
import { IoMdAnalytics } from 'react-icons/io';

const ReusableSidebar = ({ isSidebarOpen, toggleSidebar }) => {
  const { user } = useSelector((state) => state.auth);
  const location = useLocation();
  const [hoveredItem, setHoveredItem] = useState(null);
  
  // Add CSS styles for animations
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateX(-10px); }
      to { opacity: 1; transform: translateX(0); }
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .item-animate {
      opacity: 0;
      animation: fadeIn 0.3s forwards;
    }
    .delay-1 { animation-delay: 0.05s; }
    .delay-2 { animation-delay: 0.1s; }
    .delay-3 { animation-delay: 0.15s; }
    .delay-4 { animation-delay: 0.2s; }
    .delay-5 { animation-delay: 0.25s; }
    
    .hover-translate {
      transition: all 0.2s ease;
      position: relative;
    }
    .hover-translate:hover {
      transform: translateX(5px);
      background: rgba(59, 130, 246, 0.12);
    }
    .hover-translate:hover::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 0 2px 2px 0;
    }
    
    .nav-section-header {
      position: relative;
      padding-left: 8px;
    }
    .nav-section-header::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 12px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 1px;
    }

    .online-indicator {
      animation: pulse 2s ease-in-out infinite;
    }
  `;
  
  const [fullStackOpen, setFullStackOpen] = useState(false);

  const toggleFullStack = (e) => {
    e.preventDefault();
    setFullStackOpen(!fullStackOpen);
  };

  const fullStackOptions = [
    { name: "Python Full Stack", path: "/fullstack/python", icon: <FaPython size={18} className="text-green-400" /> },
    { name: "MERN Stack", path: "/fullstack/mern", icon: <FaNodeJs size={18} className="text-yellow-400" /> },
    { name: "Java Full Stack", path: "/fullstack/java", icon: <FaJava size={18} className="text-orange-400" /> },
    { name: "PHP Full Stack", path: "/fullstack/php", icon: <FaPhp size={18} className="text-indigo-400" /> },
    { name: ".NET Full Stack", path: "/fullstack/dotnet", icon: <FaCode size={18} className="text-blue-400" /> },
    { name: "Ruby on Rails", path: "/fullstack/ruby", icon: <FaCode size={18} className="text-red-400" /> },
  ];

  const courses = [
    { name: "Python", path: "/pythoncourse", icon: <FaPython size={20} className="text-green-400" />, color: "from-green-500 to-emerald-500" },
    { name: "Data Science", path: "/datascience-course", icon: <FaChartBar size={20} className="text-purple-400" />, color: "from-purple-500 to-pink-500" },
    { name: "AI Course", path: "/ai-course", icon: <FaRobot size={20} className="text-orange-400" />, color: "from-orange-500 to-red-500" },
    { name: "Machine Learning", path: "/ml-course", icon: <FaBrain size={20} className="text-indigo-400" />, color: "from-indigo-500 to-purple-500" },
  ];

  const interviewPrep = [
    { name: "System Design", path: "/sys_des_for_int", icon: <FaNetworkWired size={20} className="text-teal-400" />, color: "from-teal-500 to-blue-500" },
    { name: "DSA", path: "/data_strut", icon: <IoMdAnalytics size={20} className="text-yellow-400" />, color: "from-yellow-500 to-orange-500" },
    { name: "SQL 50", path: "/sql_50", icon: <FaDatabase size={20} className="text-green-400" />, color: "from-green-500 to-teal-500" },
    { name: "Top 60 Questions", path: "/gg_75", icon: <FaQuestion size={20} className="text-red-400" />, color: "from-red-500 to-pink-500" },
    { name: "30 Days JavaScript", path: "/30_days_js", icon: <BsFileEarmarkCode size={20} className="text-amber-400" />, color: "from-amber-500 to-yellow-500" },
  ];

  const quickLinks = [
    { name: "Home", path: "/", icon: <FaHome size={20} className="text-blue-400" />, color: "from-blue-500 to-purple-500" },
    { name: "Contests", path: "/contests", icon: <FaTrophy size={20} className="text-yellow-400" />, color: "from-yellow-500 to-red-500" },
    { name: "Community", path: "/community", icon: <FaUsers size={20} className="text-green-400" />, color: "from-green-500 to-blue-500" },
  ];

  return (
    <div
      className={`h-screen fixed left-0 z-[50] overflow-hidden transition-all duration-300 ease-out sidebar-glassmorphism ${
        isSidebarOpen ? 'translate-x-0 opacity-100' : 'translate-x-[-280px] opacity-0'
      }`}
      style={{
        width: "280px",
        top: "80px", // Start below fixed navbar
        height: "calc(100vh - 80px)", // Account for navbar height
        background: "linear-gradient(135deg, rgba(10, 15, 30, 0.95) 0%, rgba(20, 30, 60, 0.98) 50%, rgba(30, 58, 138, 0.95) 100%)",
        backdropFilter: "blur(10px)",
        WebkitBackdropFilter: "blur(10px)",
        borderRight: "1px solid rgba(59, 130, 246, 0.3)",
        position: "fixed" // Explicitly set fixed positioning
      }}
    >
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      {/* Close button */}
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleSidebar}
        className="absolute top-2 right-4 text-white/70 hover:text-white transition-colors"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </motion.button>

      {/* Sidebar content */}
      <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-blue-500/30 scrollbar-track-transparent">
        <div className="px-4 py-6 pt-16 space-y-6">
          
          {/* User Profile Section */}
          {user && (
            <div className="item-animate delay-1">
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {user.name?.charAt(0)?.toUpperCase() || "U"}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full border-2 border-[#1a3c50]" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">{user.name}</p>
                    <div className="flex items-center space-x-1">
                      <span className="w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-pulse"></span>
                      <span className="text-xs text-green-300">Online</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Quick Links */}
          <div className="item-animate delay-2">
            <h3 className="nav-section-header text-sm font-semibold text-white/90 mb-3">
              Quick Links
            </h3>
            <div className="space-y-1">
              {quickLinks.map((item, index) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onMouseEnter={() => setHoveredItem(`quick-${index}`)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={`
                    flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 hover-translate
                    ${location.pathname === item.path ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
                  `}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-800/50">
                    {item.icon}
                  </div>
                  <span className="text-white/90 font-medium">{item.name}</span>
                  {hoveredItem === `quick-${index}` && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-auto w-2 h-2 bg-blue-400 rounded-full"
                    />
                  )}
                </Link>
              ))}
            </div>
          </div>

          {/* Courses Section */}
          <div className="item-animate delay-3">
            <h3 className="nav-section-header text-sm font-semibold text-white/90 mb-3">
              Courses
            </h3>
            <div className="space-y-1">
              {/* Full Stack Development Dropdown */}
              <div className="relative">
                <button
                  onClick={toggleFullStack}
                  onMouseEnter={() => setHoveredItem('fullstack-dropdown')}
                  onMouseLeave={() => setHoveredItem(null)}
                  aria-expanded={fullStackOpen}
                  aria-controls="fullstack-menu"
                  className={`
                    w-full flex items-center justify-between px-3 py-2.5 rounded-lg transition-all duration-200 hover-translate
                    ${location.pathname.startsWith('/fullstack') ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-800/50">
                      <FaLaptopCode size={20} className="text-blue-400" />
                    </div>
                    <span className="text-white/90 font-medium">Full Stack Development</span>
                  </div>
                  <motion.div
                    animate={{ rotate: fullStackOpen ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <FaChevronDown className="text-white/70" />
                  </motion.div>
                </button>
                
                {/* Dropdown Menu */}
                {fullStackOpen && (
                  <motion.div
                    id="fullstack-menu"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="pl-10 mt-1 space-y-1"
                    role="menu"
                    aria-label="Full Stack Development options"
                  >
                    {fullStackOptions.map((option, index) => (
                      <Link
                        key={option.path}
                        to={option.path}
                        role="menuitem"
                        onMouseEnter={() => setHoveredItem(`fullstack-${index}`)}
                        onMouseLeave={() => setHoveredItem(null)}
                        className={`
                          flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 hover-translate
                          ${location.pathname === option.path ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
                        `}
                      >
                        <div className="flex items-center justify-center w-6 h-6 rounded-lg bg-gray-800/50">
                          {option.icon}
                        </div>
                        <span className="text-white/90 font-medium text-sm">{option.name}</span>
                        {hoveredItem === `fullstack-${index}` && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-auto w-1.5 h-1.5 bg-blue-400 rounded-full"
                          />
                        )}
                      </Link>
                    ))}
                  </motion.div>
                )}
              </div>
              
              {/* Regular Course Items */}
              {courses.map((course, index) => (
                <Link
                  key={course.path}
                  to={course.path}
                  onMouseEnter={() => setHoveredItem(`course-${index}`)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={`
                    flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 hover-translate
                    ${location.pathname === course.path ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
                  `}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-800/50">
                    {course.icon}
                  </div>
                  <span className="text-white/90 font-medium">{course.name}</span>
                  {hoveredItem === `course-${index}` && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-auto w-2 h-2 bg-blue-400 rounded-full"
                    />
                  )}
                </Link>
              ))}
            </div>
          </div>

          {/* Interview Preparation */}
          <div className="item-animate delay-4">
            <h3 className="nav-section-header text-sm font-semibold text-white/90 mb-3">
              Interview Prep
            </h3>
            <div className="space-y-1">
              {interviewPrep.map((item, index) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onMouseEnter={() => setHoveredItem(`prep-${index}`)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={`
                    flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 hover-translate
                    ${location.pathname === item.path ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30" : ""}
                  `}
                >
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-800/50">
                    {item.icon}
                  </div>
                  <span className="text-white/90 font-medium">{item.name}</span>
                  {hoveredItem === `prep-${index}` && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-auto w-2 h-2 bg-blue-400 rounded-full"
                    />
                  )}
                </Link>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="item-animate delay-5">
            <div className="p-4 border-t border-white/10 bg-gradient-to-r from-blue-600/10 to-purple-600/10">
              <div className="text-center">
                <p className="text-xs text-white/70 mb-2">
                  © 2024 Upcoding
                </p>
                <div className="flex justify-center space-x-3">
                  <a href="#" className="text-white/50 hover:text-white/80 transition-colors">
                    <i className="fab fa-github text-sm"></i>
                  </a>
                  <a href="#" className="text-white/50 hover:text-white/80 transition-colors">
                    <i className="fab fa-linkedin text-sm"></i>
                  </a>
                  <a href="#" className="text-white/50 hover:text-white/80 transition-colors">
                    <i className="fab fa-twitter text-sm"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ReusableSidebar;