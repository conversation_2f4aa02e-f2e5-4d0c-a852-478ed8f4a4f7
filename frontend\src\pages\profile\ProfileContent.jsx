import { useRoutes } from "react-router-dom";
import MyProfilePage from "./MyProfilePage";
import EditProfilePage from "./EditProfilePage";
import SocialAccount from "./SocialAccount";
import ChangePassword from "./ChangePassword";
import ProjectStatus from "./ProjectStatus";
import DeleteAccount from "./DeleteAccount";

const ProfileContent = () => {
  let routes = useRoutes([
    { path: "my-profile", element: <MyProfilePage /> },
    { path: "edit-profile", element: <EditProfilePage /> },
    { path: "social-accounts", element: <SocialAccount /> },
    { path: "change-password", element: <ChangePassword /> },
    { path: "project-status", element: <ProjectStatus /> },
    { path: "delete-account", element: <DeleteAccount /> },
  ]);

  return <div>{routes}</div>;
};

export default ProfileContent;
