import { useState, useEffect, useRef } from "react";
import { useParams, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { FaTimes } from "react-icons/fa";

const CourseModal = ({ isOpen, onClose }) => {
  const [step, setStep] = useState(1); // 1: Info, 2: Form
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    course: "",
    paymentOption: "credit-card",
    message: ""
  });
  
  const modalRef = useRef(null);
  const firstInputRef = useRef(null);
  const closeButtonRef = useRef(null);
  
  const location = useLocation();
  const { courseId } = useParams();
  
  // Course data mapping
  const courseInfo = {
    "python-fullstack": {
      name: "Python Full Stack",
      message: "Build your career with our Python Full Stack course.",
      color: "from-green-500 to-blue-600"
    },
    "java": {
      name: "Java Full Stack",
      message: "Master enterprise applications with our Java Full Stack course.",
      color: "from-orange-500 to-red-600"
    },
    "javascript": {
      name: "JavaScript Mastery",
      message: "Master JavaScript in 30 days and boost your frontend skills.",
      color: "from-yellow-500 to-orange-500"
    }
  };
  
  // Determine current course from URL
  const getCurrentCourse = () => {
    // Extract course name from URL path
    const pathParts = location.pathname.split('/');
    const lastPart = pathParts[pathParts.length - 1];
    
    // Check if courseId exists from useParams
    if (courseId && courseInfo[courseId]) {
      return courseInfo[courseId];
    }
    
    // Try to match the last part of the URL
    for (const [key, value] of Object.entries(courseInfo)) {
      if (lastPart.includes(key) || key.includes(lastPart)) {
        return value;
      }
    }
    
    // Default to JavaScript if no match
    return courseInfo.javascript;
  };
  
  const currentCourse = getCurrentCourse();
  
  // Set course name in form data when course changes
  useEffect(() => {
    if (currentCourse) {
      setFormData(prev => ({ ...prev, course: currentCourse.name }));
    }
  }, [location.pathname, courseId]);
  
  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    // Process form submission (e.g., API call)
    console.log("Form submitted:", formData);
    // Close modal or show success message
    onClose();
  };
  
  // Handle Buy Now button click
  const handleBuyNow = () => {
    setStep(2);
  };
  
  // Handle back button click
  const handleBack = () => {
    setStep(1);
  };
  
  // Focus trap for accessibility
  useEffect(() => {
    if (!isOpen) return;
    
    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        const focusableElements = modalRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };
    
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleTabKey);
    document.addEventListener('keydown', handleEscapeKey);
    
    // Focus first element when modal opens
    if (step === 1) {
      closeButtonRef.current?.focus();
    } else {
      firstInputRef.current?.focus();
    }
    
    return () => {
      document.removeEventListener('keydown', handleTabKey);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, step, onClose]);
  
  if (!isOpen) return null;
  
  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/70 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* Modal */}
        <motion.div
          ref={modalRef}
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="relative bg-gray-900 border border-gray-800 rounded-xl shadow-2xl w-full max-w-md overflow-hidden"
          role="dialog"
          aria-modal="true"
        >
          {/* Close button */}
          <motion.button
            ref={closeButtonRef}
            onClick={onClose}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="absolute top-4 right-4 text-gray-400 p-2 rounded-full bg-gray-800/30 transition-all"
            aria-label="Close modal"
          >
            <FaTimes />
          </motion.button>
          
          {/* Header with gradient */}
          <div className={`bg-gradient-to-r ${currentCourse.color} p-6 text-white`}>
            <h2 className="text-2xl font-bold">{currentCourse.name}</h2>
            <p className="mt-2 text-white/90">{currentCourse.message}</p>
          </div>
          
          {/* Content */}
          <div className="p-6">
            {step === 1 ? (
              <div className="space-y-6">
                <p className="text-gray-300">
                  Unlock premium course content and get:
                </p>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Full lifetime access</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>100+ hands-on exercises</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Certificate of completion</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>24/7 support</span>
                  </li>
                </ul>
                
                <div className="pt-4">
                  <motion.button
                    onClick={handleBuyNow}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full py-3 bg-gradient-to-r ${currentCourse.color} text-white font-medium rounded-lg transition-all`}
                  >
                    Buy Now
                  </motion.button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                    Name
                  </label>
                  <input
                    ref={firstInputRef}
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="course" className="block text-sm font-medium text-gray-300 mb-1">
                    Course
                  </label>
                  <input
                    type="text"
                    id="course"
                    name="course"
                    value={formData.course}
                    onChange={handleChange}
                    readOnly
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="paymentOption" className="block text-sm font-medium text-gray-300 mb-1">
                    Payment Option
                  </label>
                  <select
                    id="paymentOption"
                    name="paymentOption"
                    value={formData.paymentOption}
                    onChange={handleChange}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="credit-card">Credit Card</option>
                    <option value="paypal">PayPal</option>
                    <option value="bank-transfer">Bank Transfer</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1">
                    Additional Notes (Optional)
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows="3"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  ></textarea>
                </div>
                
                <div className="flex gap-3 pt-2">
                  <motion.button
                    type="button"
                    onClick={handleBack}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex-1 py-2 bg-gray-700 text-white font-medium rounded-lg transition-all"
                  >
                    Back
                  </motion.button>
                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`flex-1 py-2 bg-gradient-to-r ${currentCourse.color} text-white font-medium rounded-lg transition-all`}
                  >
                    Complete Purchase
                  </motion.button>
                </div>
              </form>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default CourseModal;