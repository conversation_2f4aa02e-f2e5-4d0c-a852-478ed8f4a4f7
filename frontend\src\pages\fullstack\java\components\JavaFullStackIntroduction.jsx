import React from "react";
import { motion } from "framer-motion";
import JavaFullStackDropdown from "./JavaFullStackDropdown";

const JavaFullStackIntroduction = ({ onBackToCourse }) => {
  const javaFullStackTopics = {
    "Java Backend Development": [
      {
        title: "Spring Boot Framework",
        tags: ["Web Framework", "Enterprise", "Microservices"],
        keyPoints: [
          "Build RESTful APIs with Spring Boot",
          "Create enterprise-grade applications"
        ]
      },
      {
        title: "Spring MVC",
        tags: ["Model-View-Controller", "Web Applications", "Servlets"],
        keyPoints: [
          "Develop web applications with Spring MVC",
          "Implement MVC architecture patterns"
        ]
      }
    ],
    "Frontend Integration": [
      {
        title: "Thymeleaf Templates",
        tags: ["Server-side Rendering", "HTML5", "Spring Integration"],
        keyPoints: [
          "Create dynamic HTML with Thymeleaf",
          "Integrate templates with Spring Boot"
        ]
      },
      {
        title: "RESTful Services",
        tags: ["REST", "JSON", "API Design"],
        keyPoints: [
          "Design RESTful APIs for frontend consumption",
          "Implement proper HTTP methods and status codes"
        ]
      }
    ],
    "Database Management": [
      {
        title: "JPA & Hibernate",
        tags: ["ORM", "Entity Mapping", "Transactions"],
        keyPoints: [
          "Map Java objects to database tables",
          "Manage database operations with JPA"
        ]
      },
      {
        title: "Spring Data",
        tags: ["Repositories", "Query Methods", "Pagination"],
        keyPoints: [
          "Simplify data access with Spring Data",
          "Create custom query methods"
        ]
      }
    ],
    "Enterprise Features": [
      {
        title: "Spring Security",
        tags: ["Authentication", "Authorization", "OAuth2"],
        keyPoints: [
          "Implement secure authentication",
          "Configure role-based access control"
        ]
      },
      {
        title: "Microservices",
        tags: ["Spring Cloud", "Service Discovery", "API Gateway"],
        keyPoints: [
          "Design microservices architecture",
          "Implement service communication patterns"
        ]
      }
    ]
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Java Full Stack Development</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Master Full Stack Development with Java</h3>
          <p className="text-gray-300">
            Explore our comprehensive collection of Java full stack development topics. Learn how to build 
            enterprise-grade applications from frontend to backend using Java and modern frameworks.
          </p>
        </div>
        
        {/* Dropdowns for each category */}
        <div className="space-y-4">
          {Object.entries(javaFullStackTopics).map(([category, items], index) => (
            <motion.div 
              key={category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <JavaFullStackDropdown 
                title={category} 
                items={items} 
                defaultOpen={index === 0} // Open first dropdown by default
              />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default JavaFullStackIntroduction;