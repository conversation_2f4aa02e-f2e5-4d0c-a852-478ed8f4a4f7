import React from 'react';
import { motion } from 'framer-motion';
import { FaLayerGroup, FaProjectDiagram, FaStream, FaCode, FaCube, FaThLarge, FaFileAlt, FaKey, FaObjectGroup, FaSyncAlt } from 'react-icons/fa';

const TopicGridNew = ({ topics, scrollToSection }) => {
  // Map topic icons to React Icons with colors and gradients
  const getTopicIcon = (iconName, index) => {
    const colors = [
      'from-blue-500 to-cyan-500',
      'from-purple-500 to-pink-500',
      'from-green-500 to-teal-500',
      'from-orange-500 to-red-500',
      'from-indigo-500 to-purple-500',
      'from-pink-500 to-rose-500',
      'from-yellow-500 to-orange-500',
      'from-cyan-500 to-blue-500',
      'from-emerald-500 to-green-500',
      'from-violet-500 to-purple-500'
    ];
    
    const color = colors[index % colors.length];
    const textColor = 'text-white';
    
    switch(iconName) {
      case 'fas fa-layer-group': return <FaLayerGroup className={`text-2xl ${textColor}`} />;
      case 'fas fa-project-diagram': return <FaProjectDiagram className={`text-2xl ${textColor}`} />;
      case 'fas fa-stream': return <FaStream className={`text-2xl ${textColor}`} />;
      case 'fas fa-code': return <FaCode className={`text-2xl ${textColor}`} />;
      case 'fas fa-cube': return <FaCube className={`text-2xl ${textColor}`} />;
      case 'fas fa-th-large': return <FaThLarge className={`text-2xl ${textColor}`} />;
      case 'fas fa-file-alt': return <FaFileAlt className={`text-2xl ${textColor}`} />;
      case 'fas fa-key': return <FaKey className={`text-2xl ${textColor}`} />;
      case 'fas fa-object-group': return <FaObjectGroup className={`text-2xl ${textColor}`} />;
      case 'fas fa-sync-alt': return <FaSyncAlt className={`text-2xl ${textColor}`} />;
      default: return <FaCode className={`text-2xl ${textColor}`} />;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <motion.div 
      className="p-6 bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg rounded-xl border border-white/10 shadow-lg"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
        {topics.map((topic, index) => {
          const gradientColor = [
            'from-blue-500 to-cyan-500',
            'from-purple-500 to-pink-500',
            'from-green-500 to-teal-500',
            'from-orange-500 to-red-500',
            'from-indigo-500 to-purple-500',
            'from-pink-500 to-rose-500',
            'from-yellow-500 to-orange-500',
            'from-cyan-500 to-blue-500',
            'from-emerald-500 to-green-500',
            'from-violet-500 to-purple-500'
          ][index % 10];
          
          return (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ scale: 1.05, y: -8 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => scrollToSection(topic.sectionId)}
              className="group bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm p-5 rounded-2xl shadow-lg border border-white/10 cursor-pointer hover:shadow-2xl transition-all duration-300 text-center relative overflow-hidden"
            >
              {/* Gradient background on hover */}
              <div className={`absolute inset-0 bg-gradient-to-r ${gradientColor} opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
              
              <div className={`relative w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${gradientColor} flex items-center justify-center text-white text-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                {getTopicIcon(topic.icon, index)}
              </div>
              
              <h4 className="relative text-lg font-semibold text-white group-hover:text-blue-100 transition-colors duration-300" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                {topic.title}
              </h4>
              
              {/* Subtle animation indicator */}
              <motion.div
                className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={{ scaleX: 0 }}
                whileHover={{ scaleX: 1 }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
};

export default TopicGridNew;