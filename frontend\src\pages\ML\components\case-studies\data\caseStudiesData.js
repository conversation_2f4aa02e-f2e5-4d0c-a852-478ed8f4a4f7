// Supervised Learning case studies
export const supervisedLearningStudies = [
  {
    title: "Case Study 1: Supervised Learning",
    objective:
      "Understand the fundamentals of supervised learning and implement basic supervised learning algorithms.",
    scenario:
      "You are provided with a labeled dataset containing features and corresponding target values. Develop a Python script to train a supervised learning model (e.g., Linear Regression for regression tasks or Logistic Regression for classification tasks), make predictions, and evaluate the model's performance.",
    keyConcepts: [
      "Supervised Learning",
      "Regression vs. Classification",
      "Model Training",
      "Prediction",
      "Evaluation Metrics (e.g., Mean Squared Error, Accuracy, Precision, Recall)",
    ],
    solution: `
            import pandas as pd
            from sklearn.model_selection import train_test_split
            from sklearn.linear_model import LinearRegression, LogisticRegression
            from sklearn.metrics import mean_squared_error, accuracy_score, classification_report

            data = pd.read_csv('labeled_data.csv')
            print(data.head())

            X = data.drop('target', axis=1)
            y = data['target']

            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            model = LogisticRegression(max_iter=1000)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)

            accuracy = accuracy_score(y_test, y_pred)
            print(f"Accuracy: {accuracy}")
            print("Classification Report:")
            print(classification_report(y_test, y_pred))
        `,
  },
  {
    title: "Case Study 2: Exploratory Data Analysis (EDA)",
    objective:
      "Perform exploratory data analysis to understand the underlying patterns in the data.",
    scenario:
      "Given a dataset, create visualizations and summary statistics to uncover insights and relationships between variables.",
    keyConcepts: [
      "Data visualization",
      "Correlation analysis",
      "Summary statistics",
      "Pattern recognition",
    ],
    solution: `
            import pandas as pd
            import matplotlib.pyplot as plt
            import seaborn as sns

            data = pd.read_csv('data.csv')
            print(data.describe())

            corr = data.corr()
            plt.figure(figsize=(10,8))
            sns.heatmap(corr, annot=True, cmap='coolwarm')
            plt.title('Correlation Matrix')
            plt.show()

            sns.histplot(data['feature_name'], kde=True)
            plt.title('Distribution of Feature Name')
            plt.show()

            sns.scatterplot(x='feature1', y='feature2', data=data)
            plt.title('Feature1 vs Feature2')
            plt.show()
        `,
  },
  // Add more supervised learning case studies here
];

// Unsupervised Learning case studies
export const unsupervisedLearningStudies = [
  {
    title: "Case Study 1: Clustering with K-Means",
    objective:
      "Learn how to perform K-Means clustering to identify natural groupings in data.",
    scenario:
      "Given a dataset containing customer information, apply K-Means clustering to segment customers into distinct groups based on their purchasing behavior.",
    keyConcepts: [
      "K-Means algorithm",
      "cluster initialization",
      "inertia",
      "elbow method",
      "cluster visualization",
    ],
    solution: `import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

# Loading the dataset
data = pd.read_csv('customer_data.csv')

# Selecting relevant features
features = ['Annual Income (k$)', 'Spending Score (1-100)']
X = data[features]

# Feature scaling
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Determining the optimal number of clusters using the elbow method
wcss = []
for i in range(1, 11):
    kmeans = KMeans(n_clusters=i, init='k-means++', random_state=42)
    kmeans.fit(X_scaled)
    wcss.append(kmeans.inertia_)

# Plotting the elbow graph
plt.plot(range(1, 11), wcss, marker='o')
plt.title('Elbow Method')
plt.xlabel('Number of clusters')
plt.ylabel('WCSS')
plt.show()

# From the elbow plot, assume the optimal number of clusters is 5
kmeans = KMeans(n_clusters=5, init='k-means++', random_state=42)
y_kmeans = kmeans.fit_predict(X_scaled)

# Adding cluster labels to the original data
data['Cluster'] = y_kmeans

# Visualizing the clusters
plt.scatter(data['Annual Income (k$)'], data['Spending Score (1-100)'], c=data['Cluster'], cmap='viridis')
plt.scatter(kmeans.cluster_centers_[:, 0], kmeans.cluster_centers_[:, 1], s=300, c='red', label='Centroids')
plt.title('Customer Segments')
plt.xlabel('Annual Income (k$)')
plt.ylabel('Spending Score (1-100)')
plt.legend()
plt.show()

# Displaying cluster centers
print("Cluster Centers:")
print(kmeans.cluster_centers_)`,
  },
  {
    title: "Case Study 2: Hierarchical Clustering",
    objective:
      "Understand and implement hierarchical clustering to create a dendrogram and identify clusters.",
    scenario:
      "Using the same customer dataset, perform hierarchical clustering to visualize the dendrogram and determine the optimal number of clusters.",
    keyConcepts: [
      "Agglomerative clustering",
      "dendrogram",
      "linkage methods",
      "hierarchical structure",
    ],
    solution: `import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from scipy.cluster.hierarchy import dendrogram, linkage
from sklearn.cluster import AgglomerativeClustering

# Loading the dataset
data = pd.read_csv('customer_data.csv')

# Selecting relevant features
features = ['Annual Income (k$)', 'Spending Score (1-100)']
X = data[features]

# Feature scaling
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Creating the linkage matrix
Z = linkage(X_scaled, method='ward')

# Plotting the dendrogram
plt.figure(figsize=(10, 7))
dendrogram(Z, truncate_mode='lastp', p=12, leaf_rotation=45., leaf_font_size=12., show_contracted=True)
plt.title('Dendrogram for Hierarchical Clustering')
plt.xlabel('Cluster Size')
plt.ylabel('Distance')
plt.show()

# Applying Agglomerative Clustering
hc = AgglomerativeClustering(n_clusters=5, affinity='euclidean', linkage='ward')
y_hc = hc.fit_predict(X_scaled)

# Adding cluster labels to the original data
data['Cluster'] = y_hc

# Visualizing the clusters
plt.scatter(data['Annual Income (k$)'], data['Spending Score (1-100)'], c=data['Cluster'], cmap='rainbow')
plt.title('Customer Segments (Hierarchical Clustering)')
plt.xlabel('Annual Income (k$)')
plt.ylabel('Spending Score (1-100)')
plt.show()`,
  },
  // Add more unsupervised learning case studies here
];

// Reinforcement Learning case studies
export const reinforcementLearningStudies = [
  {
    title: "Case Study 1: Implementing a Basic Q-Learning Agent",
    objective:
      "Understand the fundamentals of Q-Learning and implement a basic agent.",
    scenario:
      "Create a Q-Learning agent to solve the FrozenLake environment from OpenAI Gym. Train the agent to navigate the frozen lake without falling into holes.",
    keyConcepts: ["Q-Learning", "exploration vs. exploitation", "Q-table"],
    solution: `import gym
import numpy as np

# Initialize the FrozenLake environment
env = gym.make('FrozenLake-v1', is_slippery=False)

# Initialize Q-table
q_table = np.zeros([env.observation_space.n, env.action_space.n])

# Set learning parameters
alpha = 0.8
gamma = 0.95
epsilon = 0.1
num_episodes = 2000

# Q-Learning algorithm
for episode in range(num_episodes):
    state = env.reset()
    done = False

    while not done:
        if np.random.uniform(0, 1) < epsilon:
            action = env.action_space.sample()  # Explore
        else:
            action = np.argmax(q_table[state])  # Exploit

        next_state, reward, done, _ = env.step(action)

        # Update Q-table
        q_table[state, action] = q_table[state, action] + alpha * (reward + gamma * np.max(q_table[next_state]) - q_table[state, action])

        state = next_state

# Test the trained agent
state = env.reset()
done = False
total_reward = 0

while not done:
    action = np.argmax(q_table[state])
    state, reward, done, _ = env.step(action)
    total_reward += reward
    env.render()

print("Total Reward:", total_reward)`,
  },
  // Add more reinforcement learning case studies here
];

// Deep Learning case studies
export const deepLearningStudies = [
  {
    title: "Case Study 1: Building a Simple Neural Network with TensorFlow",
    objective:
      "Understand the basics of building a neural network using TensorFlow.",
    scenario:
      "Create a simple neural network to perform binary classification on the Iris dataset.",
    keyConcepts: ["TensorFlow basics", "neural network architecture", "binary classification"],
    solution: `import tensorflow as tf
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import OneHotEncoder

# Load and preprocess the Iris dataset
iris = load_iris()
X = iris.data
y = iris.target.reshape(-1, 1)

# Convert to binary classification (Setosa vs. others)
y = (y == 0).astype(int)

encoder = OneHotEncoder(sparse=False)
y = encoder.fit_transform(y)

# Split the dataset
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Build the neural network model
model = tf.keras.Sequential([
    tf.keras.layers.Dense(10, activation='relu', input_shape=(X_train.shape[1],)),
    tf.keras.layers.Dense(10, activation='relu'),
    tf.keras.layers.Dense(2, activation='softmax')
])

# Compile the model
model.compile(optimizer='adam',
              loss='categorical_crossentropy',
              metrics=['accuracy'])

# Train the model
model.fit(X_train, y_train, epochs=50, batch_size=16, validation_split=0.2)

# Evaluate the model
loss, accuracy = model.evaluate(X_test, y_test)
print("Test Accuracy:", accuracy)`,
  },
  // Add more deep learning case studies here
];

// NLP case studies
export const nlpStudies = [
  {
    title: "Case Study 1: Tokenization",
    objective: "Learn how to break down text into smaller units (tokens).",
    scenario:
      "Write a Python script using the NLTK library to tokenize a given sentence into words.",
    keyConcepts: ["Tokenization", "word-level tokens", "sentence splitting"],
    solution: `# Tokenizing text using NLTK
import nltk
nltk.download('punkt')
from nltk.tokenize import word_tokenize

# Sample sentence
sentence = "Natural Language Processing is fun!"
tokens = word_tokenize(sentence)
print("Tokens:", tokens)`,
  },
  // Add more NLP case studies here
];

// Feature Engineering case studies
export const featureEngineeringStudies = [
  {
    title: "Case Study 1: Introduction to Feature Engineering",
    objective:
      "Understand the concept of feature engineering in machine learning.",
    scenario:
      "Write a Python script that demonstrates basic feature engineering techniques, including encoding categorical variables and normalizing numerical features.",
    keyConcepts: [
      "Feature selection",
      "Encoding",
      "Normalization",
      "Data preprocessing",
    ],
    solution: `import pandas as pd
from sklearn.preprocessing import OneHotEncoder, StandardScaler

# Sample data
data = pd.DataFrame({
    'category': ['A', 'B', 'A', 'C'],
    'value': [10, 20, 15, 25]
})

# One-hot encoding categorical variables
encoder = OneHotEncoder(sparse=False)
encoded_categories = encoder.fit_transform(data[['category']])
encoded_df = pd.DataFrame(encoded_categories, columns=encoder.get_feature_names_out(['category']))

# Normalizing numerical features
scaler = StandardScaler()
data['value_normalized'] = scaler.fit_transform(data[['value']])

# Final dataset
final_df = pd.concat([data, encoded_df], axis=1)
print(final_df)`,
  },
  // Add more feature engineering case studies here
];

// Model Evaluation case studies
export const modelEvaluationStudies = [
  {
    title: "Case Study 1: Evaluating Model Performance",
    objective:
      "Learn how to evaluate the performance of machine learning models.",
    scenario:
      "Write a Python script to compute the accuracy of a classification model using sklearn.",
    keyConcepts: ["Model performance metrics", "accuracy", "sklearn"],
    solution: `from sklearn.metrics import accuracy_score

y_true = [0, 1, 0, 1]
y_pred = [0, 1, 1, 0]
accuracy = accuracy_score(y_true, y_pred)
print(f'Accuracy: {accuracy:.2f}')`,
  },
  // Add more model evaluation case studies here
];

// Ensemble Learning case studies
export const ensembleLearningStudies = [
  {
    title: "Case Study 1: Introduction to Ensemble Learning",
    objective: "Understand the concept of ensemble learning and its importance in improving model performance.",
    scenario: "Write a brief explanation of what ensemble learning is and its common applications.",
    keyConcepts: ["Ensemble methods", "Model accuracy", "Bias-variance tradeoff"],
    solution: `# Ensemble learning combines predictions from multiple models to improve accuracy
# Common applications include classification and regression tasks in machine learning.`
  },
  // Add more ensemble learning case studies here
];

// Dimensionality Reduction case studies
export const dimensionalityReductionStudies = [
  {
    title: "Case Study 1: Introduction to Dimensionality Reduction",
    objective: "Understand the concept and importance of dimensionality reduction.",
    scenario: "Write a brief explanation of what dimensionality reduction is and its applications in machine learning.",
    keyConcepts: ["High dimensionality", "Overfitting", "Feature extraction"],
    solution: `# Dimensionality reduction techniques help in reducing the number of features
# in a dataset while retaining essential information, thus improving model performance.`
  },
  // Add more dimensionality reduction case studies here
];

// Time Series Analysis case studies
export const timeSeriesStudies = [
  {
    title: "Case Study 1: Introduction to Time Series",
    objective: "Understand the basics of time series data.",
    scenario: "Write a brief explanation of what a time series is and its applications.",
    keyConcepts: ["Time series data", "Temporal ordering", "Seasonality"],
    solution: `# Time series data is a sequence of data points collected or recorded at specific time intervals.
# Common applications include stock prices, weather data, and economic indicators.`
  },
  // Add more time series case studies here
];

// Topics for the grid
export const topics = [
  {
    title: "Supervised Learning",
    icon: "fas fa-layer-group",
    sectionId: "supervised-learning",
  },
  {
    title: "Unsupervised Learning",
    icon: "fas fa-project-diagram",
    sectionId: "unsupervised-learning",
  },
  {
    title: "Reinforcement Learning",
    icon: "fas fa-stream",
    sectionId: "reinforcement-learning",
  },
  {
    title: "Deep Learning",
    icon: "fas fa-code",
    sectionId: "deep-learning",
  },
  {
    title: "Natural Language Processing (NLP)",
    icon: "fas fa-cube",
    sectionId: "natural-language-processing-nlp",
  },
  {
    title: "Feature Engineering",
    icon: "fas fa-th-large",
    sectionId: "feature-engineering",
  },
  {
    title: "Model Evaluation and Validation",
    icon: "fas fa-file-alt",
    sectionId: "model-evaluation-and-validation",
  },
  {
    title: "Ensemble Learning",
    icon: "fas fa-key",
    sectionId: "ensemble-learning",
  },
  {
    title: "Dimensionality Reduction",
    icon: "fas fa-object-group",
    sectionId: "dimensionality-reduction",
  },
  {
    title: "Time Series Analysis and Forecasting",
    icon: "fas fa-sync-alt",
    sectionId: "time-series-analysis-and-forecasting",
  },
];