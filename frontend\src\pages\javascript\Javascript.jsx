import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { CourseLayout, Footer } from "../../components/layout";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import { CourseModal } from "../../components/ui";
import useCourseModal from "../../hooks/useCourseModal";
import CodeEditor from "../../components/features/CodeEditor";

// Create Hero Component
const JavaScriptHero = () => {
  const { isModalOpen, openModal, closeModal } = useCourseModal();
  return (
    <>
      {/* Complex animated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <motion.divv 
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full opacity-10 blur-3xl"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 80, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-orange-400 to-red-600 rounded-full opacity-10 blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(i) * 20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              delay: i * 0.5,
            }}
            className={`absolute w-2 h-2 bg-yellow-300 rounded-full`}
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto pt-20 pb-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="text-center lg:text-left">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-yellow-500/20 border border-yellow-400/30 rounded-full text-yellow-300 text-sm font-medium mb-6 backdrop-blur-sm"
            >
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              🚀 30 Days JavaScript Challenge
            </motion.div>

            <motion.h1
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
            >
              Master{" "}
              <span className="bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
                JavaScript
              </span>
              <br />
              in{" "}
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                30 Days
              </span>
            </motion.h1>

            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-xl md:text-2xl mb-8 text-white max-w-2xl mx-auto lg:mx-0 leading-relaxed"
            >
              Complete JavaScript mastery with daily challenges, interview questions, and real-world projects. Build your coding confidence one day at a time.
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-wrap gap-4 justify-center lg:justify-start items-center mb-12"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={openModal}
                className="px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold rounded-xl text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
              >
                Start Challenge →
              </motion.button>
              
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-white">30</div>
                <span className="text-gray-300">Days Challenge</span>
              </div>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-wrap justify-center lg:justify-start items-center gap-6 text-sm"
            >
              <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="text-yellow-400">⭐</span>
                <span className="text-gray-200">300+ Coding Challenges</span>
              </div>
              <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="text-green-400">✓</span>
                <span className="text-gray-200">Real Interview Questions</span>
              </div>
              <div className="flex items-center gap-2 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm">
                <span className="text-blue-400">📊</span>
                <span className="text-gray-200">Progress Tracking</span>
              </div>
            </motion.div>
          </div>

          {/* Right Column - Interactive Code Preview */}
          <motion.div
            initial={{ x: 30, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Code Terminal */}
            <div className="bg-gray-900/80 backdrop-blur-xl border border-gray-800/50 rounded-xl overflow-hidden shadow-[0_5px_30px_rgba(0,0,0,0.25)]">
              <div className="flex items-center gap-2 px-4 py-3 bg-gray-800/90 border-b border-gray-700/50">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="ml-4 text-gray-400 text-sm">30-days-js-challenge.js</span>
              </div>
              
              <div className="p-6 font-mono text-sm leading-relaxed">
                <div className="text-purple-400 mb-2">// Day 1: Variables and Data Types</div>
                <div className="text-blue-400 mb-1">const</div>
                <div className="text-white mb-1"> challenge = {"{"}</div>
                <div className="text-yellow-400 ml-4 mb-1">  day: 1,</div>
                <div className="text-yellow-400 ml-4 mb-1">  topic: "Variables",</div>
                <div className="text-yellow-400 ml-4 mb-1">  difficulty: "Beginner",</div>
                <div className="text-green-400 ml-4 mb-1">  completed: true</div>
                <div className="text-white mb-3">{"}"};</div>
                
                <div className="text-purple-400 mb-2">// Your Progress</div>
                <div className="text-blue-400 mb-1">function</div>
                <div className="text-white mb-1"> getProgress() {"{"}</div>
                <div className="text-blue-400 ml-4 mb-1">  return</div>
                <div className="text-green-400 ml-4 mb-1"> "Day 15/30 Complete! 🎉";</div>
                <div className="text-white mb-3">{"}"}</div>
                
                <motion.div
                  animate={{ opacity: [1, 0.5, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="text-green-400"
                >
                  ▸ Keep coding! You're halfway there! 🚀
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Course Purchase Modal */}
      <CourseModal isOpen={isModalOpen} onClose={closeModal} />
    </>
  );
};

// Navigation Buttons Component
const NavigationButtons = () => {
  const navItems = [
    { id: 'challenges', title: 'Daily Challenges', icon: '🗓️' },
    { id: 'projects', title: 'Real-Time Projects', icon: '📁' },
    { id: 'interview', title: 'Interview Questions', icon: '📚' },
    { id: 'checklist', title: 'Progress Checklist', icon: '✅' },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
      {navItems.map((item, index) => (
        <motion.div
          key={item.id}
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6 text-center hover:bg-white/10 transition-all duration-300 cursor-pointer group"
        >
          <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">
            {item.icon}
          </div>
          <h3 className="text-white font-semibold">{item.title}</h3>
        </motion.div>
      ))}
    </div>
  );
};

// Difficulty Section Component
const DifficultySection = ({ sections }) => {
  return (
    <div className="space-y-8">
      {sections.map((section, sectionIndex) => (
        <motion.div
          key={section.id}
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: sectionIndex * 0.2 }}
          className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8"
        >
          <div className="flex items-center gap-4 mb-6">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              section.difficulty === 'Easy' ? 'bg-green-500/20 text-green-400' :
              section.difficulty === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
              'bg-red-500/20 text-red-400'
            }`}>
              <i className={section.icon}></i>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                section.difficulty === 'Easy' ? 'bg-green-500/20 text-green-400' :
                section.difficulty === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400'
              }`}>
                {section.difficulty}
              </span>
            </div>
          </div>

          <div className="grid gap-6">
            {section.questions.map((question, questionIndex) => (
              <div
                key={questionIndex}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-6"
              >
                <h3 className="text-xl font-semibold text-white mb-4">{question.title}</h3>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {question.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <ul className="space-y-2">
                  {question.keyPoints.map((point, pointIndex) => (
                    <li key={pointIndex} className="text-gray-300 flex items-start">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      {point}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Interview Checklist Component
const InterviewChecklist = ({ questions }) => {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleAnswer = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <motion.div
      initial={{ y: 30, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6 }}
      className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8"
    >
      <h2 className="text-3xl font-bold text-white mb-8 text-center">
        📋 JavaScript Interview Checklist
      </h2>
      
      <div className="space-y-4">
        {questions.map((item, index) => (
          <div
            key={item.id}
            className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => toggleAnswer(index)}
              className="w-full px-6 py-4 text-left hover:bg-white/10 transition-all duration-300 flex items-center justify-between"
            >
              <span className="text-white font-medium">
                {item.id}. {item.question}
              </span>
              <span className={`text-white transition-transform duration-300 ${
                activeIndex === index ? 'rotate-180' : ''
              }`}>
                ▼
              </span>
            </button>
            
            {activeIndex === index && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="px-6 pb-4"
              >
                <p className="text-gray-300">{item.answer}</p>
              </motion.div>
            )}
          </div>
        ))}
      </div>
    </motion.div>
  );
};

const Javascript = () => {
  const { isSidebarOpen, toggleSidebar } = useSidebarState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showPremiumOverlay, setShowPremiumOverlay] = useState(false);
  
  const handleShowPremiumOverlay = () => {
    setShowPremiumOverlay(true);
  };

  // Handle scroll to top visibility
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const sections = [
    {
      id: "beginner-level",
      title: "Beginner Level",
      icon: "fas fa-leaf",
      difficulty: "Easy",
      questions: [
        {
          title: 'Explain "Hoisting" in JavaScript',
          tags: ["Concepts", "Variables", "Scope"],
          keyPoints: [
            "Variable and function declaration behavior",
            'Understanding "temporal dead zone"',
          ],
        },
        {
          title: 'Differentiate between "==" and "===" in JavaScript',
          tags: ["Operators", "Type Conversion"],
          keyPoints: ['Type coercion with "=="', 'Strict equality with "==="'],
        },
      ],
    },
    {
      id: "intermediate-level", 
      title: "Intermediate Level",
      icon: "fas fa-fire",
      difficulty: "Medium",
      questions: [
        {
          title: "Explain Closures in JavaScript",
          tags: ["Functions", "Scope", "Closures"],
          keyPoints: [
            "Lexical scoping and nested functions",
            "Use cases: data hiding, function factories",
          ],
        },
        {
          title: "What is Event Delegation in JavaScript?",
          tags: ["Events", "DOM Manipulation"],
          keyPoints: ["Event handling efficiency", "Reducing memory footprint"],
        },
      ],
    },
    {
      id: "advanced-level",
      title: "Advanced Level", 
      icon: "fas fa-bolt",
      difficulty: "Hard",
      questions: [
        {
          title: 'Explain the "this" Keyword in JavaScript',
          tags: ["Context", "Functions", "Binding"],
          keyPoints: [
            "Global, object, and function context",
            'Arrow functions and lexical "this"',
          ],
        },
        {
          title: "Explain Asynchronous JavaScript (Promises, Async/Await)",
          tags: ["Asynchronous Programming", "Promises", "Async/Await"],
          keyPoints: [
            "Handling asynchronous code with promises",
            "Using async/await for readability",
          ],
        },
      ],
    },
  ];

  const javascriptQuestions = [
    {
      id: 1,
      question: "Explain the use of 'this' in JavaScript",
      answer: "The 'this' keyword refers to the current context, which can vary based on function invocation."
    },
    {
      id: 2,
      question: "Explain the Prototype Chain",
      answer: "JavaScript uses prototype-based inheritance, allowing objects to inherit properties from other objects."
    },
    {
      id: 3,
      question: "What are Callbacks, Promises, and Async/Await?",
      answer: "Callbacks allow functions to be passed as arguments; promises handle asynchronous events, and async/await makes async code more readable."
    },
    {
      id: 4,
      question: "Explain Event Bubbling and Capturing",
      answer: "Event bubbling and capturing describe the event propagation phases, controlling how events are handled in the DOM."
    },
    {
      id: 5,
      question: "What are arrow functions?",
      answer: "Shorter syntax for function expressions, with no binding to 'this'."
    },
    // Add more questions...
  ];

  return (
    <CourseLayout 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="30 Days JavaScript"
    >
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-[linear-gradient(114deg,#1a3c50,#010509)] text-white min-h-screen flex items-center px-6">
        <JavaScriptHero />
      </section>

      {/* Main Content */}
      <div className="min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] py-16">
        <div className="max-w-6xl mx-auto px-4">
          {/* Navigation Buttons */}
          <NavigationButtons />

          {/* Difficulty Sections */}
          <DifficultySection sections={sections} />

          {/* Interview Checklist */}
          <div className="mt-16">
            <InterviewChecklist questions={javascriptQuestions} />
          </div>
          
          {/* JavaScript Lab Environment */}
          <div className="mt-16">
            <h2 className="text-3xl font-bold text-white mb-6">Practice Lab</h2>
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-4">
              <CodeEditor />
            </div>
          </div>

          {/* Course Reviews */}
          <div className="mt-16">
            <ReviewManager
              courseId="javascript-course"
              courseName="30 Days JavaScript Course"
              showWriteReview={true}
              showStats={true}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />

      {/* Floating Back to Top Button */}
      {showScrollTop && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 z-50"
        >
          ↑
        </motion.button>
      )}
    </CourseLayout>
  );
};

export default Javascript;
