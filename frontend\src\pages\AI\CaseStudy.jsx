import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaCode, FaLightbulb, FaRobot, FaChartBar, FaDatabase, FaBrain, FaNetworkWired, FaFileCode, FaImage, FaLanguage, FaServer, FaCloud, FaMicrochip, FaPython, FaEye, FaGamepad, FaBalanceScale, FaHospital, FaPlug, FaFileAlt } from "react-icons/fa";
import { categories } from './components/case-studies/caseStudiesData';

function CaseStudy() {
  const [activeCategory, setActiveCategory] = useState(categories[0].id);
  const [expandedStudy, setExpandedStudy] = useState(null);
  const [showCode, setShowCode] = useState({});

  const selectedCategoryObj = categories.find(cat => cat.id === activeCategory);
  const caseStudies = selectedCategoryObj?.studies || [];

  const toggleCode = (index) => {
    setShowCode(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  }; 

  const getDifficultyBadge = (difficulty) => {
    switch(difficulty) {
      case 'Beginner':
        return <span className="px-2 py-1 text-xs font-medium bg-green-900 text-green-300 rounded-full">Beginner</span>;
      case 'Intermediate':
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-900 text-yellow-300 rounded-full">Intermediate</span>;
      case 'Advanced':
        return <span className="px-2 py-1 text-xs font-medium bg-red-900 text-red-300 rounded-full">Advanced</span>;
      default:
        return null;
    }
  };

  const getCategoryIcon = (categoryId) => {
    switch(categoryId) {
      case 'ai': return <FaRobot className="text-purple-400" size={18} />;
      case 'python': return <FaPython className="text-green-400" size={18} />;
      case 'nlp': return <FaFileAlt className="text-blue-400" size={18} />;
      case 'cv': return <FaEye className="text-yellow-400" size={18} />;
      case 'rl': return <FaGamepad className="text-red-400" size={18} />;
      case 'robotics': return <FaRobot className="text-indigo-400" size={18} />;
      case 'ethics': return <FaBalanceScale className="text-gray-400" size={18} />;
      case 'healthcare': return <FaHospital className="text-emerald-400" size={18} />;
      case 'bigdata': return <FaDatabase className="text-orange-400" size={18} />;
      case 'iot': return <FaPlug className="text-sky-400" size={18} />;
      default: return <FaLightbulb className="text-blue-400" size={18} />;
    }
  };

  const getConceptIcon = (concept) => {
    // Neural networks and deep learning
    if (concept.includes("Neural") || concept.includes("Deep Learning"))
      return <FaBrain className="text-blue-400" />;
      
    // Data related
    if (concept.includes("Data") || concept.includes("Analytics"))
      return <FaDatabase className="text-green-400" />;
      
    // Programming and algorithms
    if (concept.includes("Algorithm") || concept.includes("Code"))
      return <FaCode className="text-purple-400" />;
      
    // Visualization
    if (concept.includes("Visualization") || concept.includes("Chart"))
      return <FaChartBar className="text-yellow-400" />;
      
    // Computer vision
    if (concept.includes("Vision") || concept.includes("Image"))
      return <FaImage className="text-orange-400" />;
      
    // NLP
    if (concept.includes("Language") || concept.includes("NLP") || concept.includes("Text"))
      return <FaLanguage className="text-indigo-400" />;
      
    // Infrastructure
    if (concept.includes("Server") || concept.includes("Infrastructure"))
      return <FaServer className="text-gray-400" />;
      
    // Cloud
    if (concept.includes("Cloud") || concept.includes("AWS") || concept.includes("Azure"))
      return <FaCloud className="text-sky-400" />;
      
    // Hardware
    if (concept.includes("Hardware") || concept.includes("GPU") || concept.includes("CPU"))
      return <FaMicrochip className="text-red-400" />;
      
    // Networks
    if (concept.includes("Network") || concept.includes("Graph"))
      return <FaNetworkWired className="text-teal-400" />;
      
    // Python
    if (concept.includes("Python") || concept.includes("TensorFlow") || concept.includes("PyTorch"))
      return <FaFileCode className="text-yellow-400" />;
      
    // Default
    return <FaLightbulb className="text-amber-400" />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">AI Case Studies</h1>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Explore real-world applications of artificial intelligence through interactive case studies
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2 p-2 bg-gray-800 rounded-lg border border-gray-700 mb-6">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                activeCategory === category.id
                  ? `bg-gradient-to-r ${category.color} text-white`
                  : 'text-gray-300 hover:bg-gray-700'
              }`}
            >
              <span className="text-sm">
                {getCategoryIcon(category.id)}
              </span>
              <span className="hidden sm:inline text-sm">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Case Studies */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="grid gap-6"
          >
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                layout
                className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden"
              >
                {/* Card Header */}
                <div 
                  className="p-4 cursor-pointer"
                  onClick={() => setExpandedStudy(expandedStudy === index ? null : index)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-bold text-white">{study.title}</h3>
                        {study.difficulty && getDifficultyBadge(study.difficulty)}
                      </div>
                      <p className="text-gray-300 text-sm">{study.objective}</p>
                    </div>
                    <button className="text-blue-400 hover:text-blue-300 transition-colors">
                      {expandedStudy === index ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>

                {/* Expanded Content */}
                <AnimatePresence>
                  {expandedStudy === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-gray-700"
                    >
                      <div className="p-4 space-y-4">
                        {/* Scenario */}
                        <div>
                          <h4 className="text-base font-semibold text-white mb-2 flex items-center gap-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            Scenario
                          </h4>
                          <p className="text-gray-300 text-sm">{study.scenario}</p>
                        </div>

                        {/* Key Concepts */}
                        <div>
                          <h4 className="text-base font-semibold text-white mb-2 flex items-center gap-2">
                            <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                            Key Concepts
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {study.keyConcepts.map((concept, idx) => (
                              <div key={idx} className="flex items-center gap-1 px-2 py-1 bg-gray-700 rounded-full text-xs">
                                {getConceptIcon(concept)}
                                <span className="text-gray-300">{concept}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Solution */}
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="text-base font-semibold text-white flex items-center gap-2">
                              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                              Solution
                            </h4>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleCode(index);
                              }}
                              className="text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors"
                            >
                              {showCode[index] ? "Hide Code" : "Show Code"}
                            </button>
                          </div>
                          
                          {showCode[index] && (
                            <div className="relative">
                              <pre className="bg-gray-900 text-gray-100 p-3 rounded-lg overflow-x-auto text-xs leading-relaxed border border-gray-700">
                                <code>{study.solution}</code>
                              </pre>
                              <button 
                                onClick={() => navigator.clipboard.writeText(study.solution)}
                                className="absolute top-2 right-2 p-1 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                                title="Copy code"
                              >
                                <svg className="w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Empty State */}
        {caseStudies.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-500 mb-3">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.881-6.08 2.33l-.147.083A7.994 7.994 0 0112 21a7.994 7.994 0 016.227-2.587l-.147-.083z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-300 mb-2">No case studies found</h3>
            <p className="text-gray-400 text-sm">Try selecting a different category.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default CaseStudy;