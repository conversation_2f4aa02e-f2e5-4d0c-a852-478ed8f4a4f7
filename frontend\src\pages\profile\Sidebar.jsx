import { ImUserTie } from "react-icons/im";
import { FaUserEdit } from "react-icons/fa";
import { BiSolidUserAccount } from "react-icons/bi";
import { RiLockPasswordFill } from "react-icons/ri";
import { PiProjectorScreenFill } from "react-icons/pi";
import { RiDeleteBin7Fill } from "react-icons/ri";
import { Link } from "react-router-dom";

const Sidebar = ({closeSidebar}) => {
  return (
    <>
      <div className="profile-sidebar">
        <ul className="sidebar-list">
          <Link to={"/profile/my-profile"} className="profile-link" onClick={closeSidebar}>
            <li className="sidebar-list-icons">
              <ImUserTie size={20} style={{ color: "black" }} /> My profile
            </li>
          </Link>
          <Link to={"/profile/edit-profile"} className="profile-link" onClick={closeSidebar}>
            <li className="sidebar-list-icons">
              <FaUserEdit size={20} style={{ color: "black" }} /> Edit profile
            </li>
          </Link>
          <Link to={"/profile/social-accounts"} className="profile-link" onClick={closeSidebar}>
            <li className="sidebar-list-icons">
              <BiSolidUserAccount size={20} style={{ color: "black" }} /> Social
              Accounts
            </li>
          </Link>
          <Link to={"/profile/change-password"} className="profile-link" onClick={closeSidebar}>
            <li className="sidebar-list-icons">
              <RiLockPasswordFill size={20} style={{ color: "black" }} /> Change
              Password
            </li>
          </Link>
          <Link to={"/profile/project-status"} className="profile-link" onClick={closeSidebar}>
            <li className="sidebar-list-icons">
              <PiProjectorScreenFill size={20} style={{ color: "black" }} />{" "}
              Projects Status
            </li>
          </Link>
          <Link to={"/profile/delete-account"} className="profile-link" onClick={closeSidebar}>
            <li className="delete-icons" style={{ color: "red" }}>
              <RiDeleteBin7Fill size={20} /> Delete Account
            </li>
          </Link>
        </ul>
      </div>
    </>
  );
};

export default Sidebar;
