import React from "react";
import Introduction from "./Introduction";
import Chapter from "./Chapter";
import CaseStudy from "./CaseStudy";
import FAQS from "./FAQS";
import AIHero from "./components/AIHero";
import AILabEnvironmentNew from "./components/AILabEnvironmentNew";
import AILiveClasses from "./components/AILiveClasses";
import AIPremiumModal from "./components/AIPremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import { FaRobot, FaFlask, FaGraduationCap, FaBook, FaSearch, FaQuestionCircle } from 'react-icons/fa';

const ArtificialIntelligence = () => {
  const courseConfig = {
    title: "Artificial Intelligence Course Resources",
    subtitle: "Master AI through comprehensive modules, cutting-edge techniques, live workshops, and practical implementations",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-400"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn AI fundamentals and core concepts",
        icon: "🤖",
        component: Introduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with AI frameworks and tools",
        icon: "🔬",
        component: AILabEnvironmentNew,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join expert-led AI workshops",
        icon: "🎓",
        component: AILiveClasses,
        props: {}
      },
      {
        id: "Chapter",
        title: "AI Learning Chapters",
        description: "Dive deep into AI algorithms and techniques",
        icon: "📖",
        component: Chapter,
        props: {}
      },
      {
        id: "CaseStudy",
        title: "AI Case Studies",
        description: "Explore real-world AI applications",
        icon: "🔍",
        component: CaseStudy,
        props: {}
      },
      {
        id: "FAQS",
        title: "Frequently Asked Questions",
        description: "Get answers to common AI questions",
        icon: "❓",
        component: FAQS,
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="ai-course"
            courseName="Artificial Intelligence Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={AIHero}
      PremiumModalComponent={AIPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default ArtificialIntelligence;
