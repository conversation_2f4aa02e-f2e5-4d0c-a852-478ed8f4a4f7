import { 
  FaCheckCircle, 
  FaClock, 
  FaPause, 
  FaCode,
  FaGithub,
  FaExternalLinkAlt,
  FaCalendarAlt,
  FaPlay,
  FaAward,
  FaExclamationTriangle,
  FaChartLine
} from "react-icons/fa";

const ProjectCard = ({ project, calculateDaysRemaining }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return <FaCheckCircle className="text-green-400" />;
      case "in-progress":
        return <FaClock className="text-blue-400" />;
      case "paused":
        return <FaPause className="text-yellow-400" />;
      default:
        return <FaCode className="text-slate-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "from-green-900/60 via-emerald-800/70 to-slate-800/80 border-green-500/30 hover:border-green-400/50";
      case "in-progress":
        return "from-blue-900/60 via-cyan-800/70 to-slate-800/80 border-blue-500/30 hover:border-cyan-400/50";
      case "paused":
        return "from-yellow-900/60 via-orange-800/70 to-slate-800/80 border-yellow-500/30 hover:border-orange-400/50";
      default:
        return "from-slate-900/60 via-slate-800/70 to-slate-800/80 border-slate-500/30 hover:border-slate-400/50";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-300 border-red-500/30";
      case "medium":
        return "bg-yellow-500/20 text-yellow-300 border-yellow-500/30";
      case "low":
        return "bg-green-500/20 text-green-300 border-green-500/30";
      default:
        return "bg-slate-500/20 text-slate-300 border-slate-500/30";
    }
  };

  return (
    <div
      className={`relative bg-gradient-to-br ${getStatusColor(project.status)} backdrop-blur-xl rounded-2xl shadow-xl p-6 border transition-all duration-500 group overflow-hidden`}
    >
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>
      
      <div className="relative z-10">
        {/* Project Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {getStatusIcon(project.status)}
            <div>
              <h3 className="text-xl font-bold text-white mb-1">{project.title}</h3>
              <p className="text-slate-300 text-sm">{project.description}</p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-xs font-bold border ${getPriorityColor(project.priority)}`}>
            {project.priority.toUpperCase()}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-slate-300">Progress</span>
            <span className="text-sm font-semibold text-white">{project.progress}%</span>
          </div>
          <div className="w-full bg-slate-700/50 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                project.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                project.status === 'in-progress' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                'bg-gradient-to-r from-yellow-500 to-orange-500'
              }`}
              style={{ width: `${project.progress}%` }}
            ></div>
          </div>
        </div>

        {/* Technologies */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-slate-800/50 text-slate-300 text-xs rounded-lg border border-slate-600/30"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>

        {/* Project Details */}
        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div>
            <div className="text-slate-400 mb-1">Start Date</div>
            <div className="text-white font-semibold flex items-center gap-1">
              <FaCalendarAlt className="text-xs" />
              {new Date(project.startDate).toLocaleDateString()}
            </div>
          </div>
          <div>
            <div className="text-slate-400 mb-1">
              {project.status === 'completed' ? 'Completed' : 'Deadline'}
            </div>
            <div className="text-white font-semibold flex items-center gap-1">
              <FaCalendarAlt className="text-xs" />
              {project.status === 'completed' 
                ? new Date(project.completedDate).toLocaleDateString()
                : new Date(project.deadline).toLocaleDateString()
              }
            </div>
          </div>
          <div>
            <div className="text-slate-400 mb-1">Time Spent</div>
            <div className="text-white font-semibold flex items-center gap-1">
              <FaClock className="text-xs" />
              {project.timeSpent}
            </div>
          </div>
          <div>
            <div className="text-slate-400 mb-1">Estimated</div>
            <div className="text-white font-semibold flex items-center gap-1">
              <FaClock className="text-xs" />
              {project.estimatedTime}
            </div>
          </div>
        </div>

        {/* Special Status Indicators */}
        {project.status === 'completed' && project.rating && (
          <div className="mb-4 p-3 bg-green-900/30 border border-green-500/30 rounded-lg">
            <div className="flex items-center gap-2">
              <FaAward className="text-green-400" />
              <span className="text-green-300 text-sm font-semibold">
                Project Rating: {project.rating}/5.0
              </span>
            </div>
          </div>
        )}

        {project.status === 'paused' && project.pauseReason && (
          <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-500/30 rounded-lg">
            <div className="flex items-center gap-2">
              <FaExclamationTriangle className="text-yellow-400" />
              <span className="text-yellow-300 text-sm font-semibold">
                Paused: {project.pauseReason}
              </span>
            </div>
          </div>
        )}

        {project.status === 'in-progress' && (
          <div className="mb-4 p-3 bg-blue-900/30 border border-blue-500/30 rounded-lg">
            <div className="flex items-center gap-2">
              <FaChartLine className="text-blue-400" />
              <span className="text-blue-300 text-sm font-semibold">
                {calculateDaysRemaining(project.deadline) > 0 
                  ? `${calculateDaysRemaining(project.deadline)} days remaining`
                  : `${Math.abs(calculateDaysRemaining(project.deadline))} days overdue`
                }
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {project.githubUrl && (
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white rounded-lg transition-all duration-300 border border-slate-600/30 hover:border-slate-500/50"
            >
              <FaGithub className="text-sm" />
              <span className="text-sm font-semibold">GitHub</span>
            </a>
          )}
          
          {project.liveUrl && (
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600/50 to-purple-600/50 hover:from-blue-600/70 hover:to-purple-600/70 text-white rounded-lg transition-all duration-300 border border-blue-500/30 hover:border-purple-500/50"
            >
              <FaExternalLinkAlt className="text-sm" />
              <span className="text-sm font-semibold">Live Demo</span>
            </a>
          )}
          
          {project.status === 'in-progress' && (
            <button className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-green-600/50 to-emerald-600/50 hover:from-green-600/70 hover:to-emerald-600/70 text-white rounded-lg transition-all duration-300 border border-green-500/30 hover:border-emerald-500/50">
              <FaPlay className="text-sm" />
              <span className="text-sm font-semibold">Continue</span>
            </button>
          )}
        </div>

        {/* Last Updated */}
        <div className="mt-4 pt-4 border-t border-slate-600/30">
          <div className="text-xs text-slate-400">
            Last updated: {new Date(project.lastUpdated).toLocaleDateString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
